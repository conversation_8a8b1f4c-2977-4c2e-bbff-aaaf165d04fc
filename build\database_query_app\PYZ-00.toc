('C:\\Users\\<USER>\\Desktop\\test\\build\\database_query_app\\PYZ-00.pyz',
 [('PIL', 'D:\\SeaPython\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\SeaPython\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util', 'D:\\SeaPython\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'D:\\SeaPython\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\SeaPython\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'D:\\SeaPython\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\SeaPython\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\SeaPython\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\SeaPython\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\SeaPython\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'D:\\SeaPython\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_markupbase', 'D:\\SeaPython\\Lib\\_markupbase.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\SeaPython\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'D:\\SeaPython\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\SeaPython\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\SeaPython\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\SeaPython\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\SeaPython\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\SeaPython\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'D:\\SeaPython\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\SeaPython\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'D:\\SeaPython\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'D:\\SeaPython\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\SeaPython\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\SeaPython\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'D:\\SeaPython\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'D:\\SeaPython\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'D:\\SeaPython\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'D:\\SeaPython\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'D:\\SeaPython\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'D:\\SeaPython\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\SeaPython\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'D:\\SeaPython\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'D:\\SeaPython\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\SeaPython\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\SeaPython\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'D:\\SeaPython\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\SeaPython\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\SeaPython\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\SeaPython\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\SeaPython\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\SeaPython\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\SeaPython\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\SeaPython\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\SeaPython\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\SeaPython\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\SeaPython\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\SeaPython\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\SeaPython\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\SeaPython\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\SeaPython\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\SeaPython\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\SeaPython\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\SeaPython\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\SeaPython\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\SeaPython\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\SeaPython\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\SeaPython\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\SeaPython\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'D:\\SeaPython\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\SeaPython\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\SeaPython\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\SeaPython\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\SeaPython\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'D:\\SeaPython\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\SeaPython\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\SeaPython\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\SeaPython\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\SeaPython\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\SeaPython\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\SeaPython\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\SeaPython\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\SeaPython\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\SeaPython\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\SeaPython\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\SeaPython\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\SeaPython\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\SeaPython\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli', 'D:\\SeaPython\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('bs4', 'D:\\SeaPython\\Lib\\site-packages\\bs4\\__init__.py', 'PYMODULE'),
  ('bs4._deprecation',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css', 'D:\\SeaPython\\Lib\\site-packages\\bs4\\css.py', 'PYMODULE'),
  ('bs4.dammit',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\SeaPython\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'D:\\SeaPython\\Lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'D:\\SeaPython\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\SeaPython\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\SeaPython\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'D:\\SeaPython\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'D:\\SeaPython\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\SeaPython\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\SeaPython\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\SeaPython\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\SeaPython\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\SeaPython\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'D:\\SeaPython\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\SeaPython\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\SeaPython\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\SeaPython\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\SeaPython\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\SeaPython\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\SeaPython\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\SeaPython\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\SeaPython\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\SeaPython\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\SeaPython\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'D:\\SeaPython\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\SeaPython\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'D:\\SeaPython\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\SeaPython\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\SeaPython\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\SeaPython\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\SeaPython\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\SeaPython\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\SeaPython\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\SeaPython\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\SeaPython\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\SeaPython\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\SeaPython\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\SeaPython\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\SeaPython\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\SeaPython\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\SeaPython\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\SeaPython\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\SeaPython\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\SeaPython\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\SeaPython\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\SeaPython\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\SeaPython\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\SeaPython\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\SeaPython\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\SeaPython\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\SeaPython\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\SeaPython\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'D:\\SeaPython\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\SeaPython\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\SeaPython\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'D:\\SeaPython\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\SeaPython\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\SeaPython\\Lib\\fractions.py', 'PYMODULE'),
  ('fsspec',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\__init__.py',
   'PYMODULE'),
  ('fsspec._version',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\_version.py',
   'PYMODULE'),
  ('fsspec.archive',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\archive.py',
   'PYMODULE'),
  ('fsspec.asyn',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\asyn.py',
   'PYMODULE'),
  ('fsspec.caching',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\caching.py',
   'PYMODULE'),
  ('fsspec.callbacks',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\callbacks.py',
   'PYMODULE'),
  ('fsspec.compression',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\compression.py',
   'PYMODULE'),
  ('fsspec.config',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\config.py',
   'PYMODULE'),
  ('fsspec.conftest',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\conftest.py',
   'PYMODULE'),
  ('fsspec.core',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\core.py',
   'PYMODULE'),
  ('fsspec.dircache',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\dircache.py',
   'PYMODULE'),
  ('fsspec.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\exceptions.py',
   'PYMODULE'),
  ('fsspec.fuse',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\fuse.py',
   'PYMODULE'),
  ('fsspec.generic',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\generic.py',
   'PYMODULE'),
  ('fsspec.gui',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\gui.py',
   'PYMODULE'),
  ('fsspec.implementations',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\__init__.py',
   'PYMODULE'),
  ('fsspec.implementations.arrow',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\arrow.py',
   'PYMODULE'),
  ('fsspec.implementations.asyn_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\asyn_wrapper.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_mapper',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\cache_mapper.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_metadata',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\cache_metadata.py',
   'PYMODULE'),
  ('fsspec.implementations.cached',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\cached.py',
   'PYMODULE'),
  ('fsspec.implementations.dask',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\dask.py',
   'PYMODULE'),
  ('fsspec.implementations.data',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\data.py',
   'PYMODULE'),
  ('fsspec.implementations.dbfs',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\dbfs.py',
   'PYMODULE'),
  ('fsspec.implementations.dirfs',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\dirfs.py',
   'PYMODULE'),
  ('fsspec.implementations.ftp',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\ftp.py',
   'PYMODULE'),
  ('fsspec.implementations.gist',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\gist.py',
   'PYMODULE'),
  ('fsspec.implementations.git',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\git.py',
   'PYMODULE'),
  ('fsspec.implementations.github',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\github.py',
   'PYMODULE'),
  ('fsspec.implementations.http',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\http.py',
   'PYMODULE'),
  ('fsspec.implementations.http_sync',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\http_sync.py',
   'PYMODULE'),
  ('fsspec.implementations.jupyter',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\jupyter.py',
   'PYMODULE'),
  ('fsspec.implementations.libarchive',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\libarchive.py',
   'PYMODULE'),
  ('fsspec.implementations.local',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\local.py',
   'PYMODULE'),
  ('fsspec.implementations.memory',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\memory.py',
   'PYMODULE'),
  ('fsspec.implementations.reference',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\reference.py',
   'PYMODULE'),
  ('fsspec.implementations.sftp',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\sftp.py',
   'PYMODULE'),
  ('fsspec.implementations.smb',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\smb.py',
   'PYMODULE'),
  ('fsspec.implementations.tar',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\tar.py',
   'PYMODULE'),
  ('fsspec.implementations.webhdfs',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\webhdfs.py',
   'PYMODULE'),
  ('fsspec.implementations.zip',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\zip.py',
   'PYMODULE'),
  ('fsspec.json',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\json.py',
   'PYMODULE'),
  ('fsspec.mapping',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\mapping.py',
   'PYMODULE'),
  ('fsspec.parquet',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\parquet.py',
   'PYMODULE'),
  ('fsspec.registry',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\registry.py',
   'PYMODULE'),
  ('fsspec.spec',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\spec.py',
   'PYMODULE'),
  ('fsspec.transaction',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\transaction.py',
   'PYMODULE'),
  ('fsspec.utils',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\utils.py',
   'PYMODULE'),
  ('ftplib', 'D:\\SeaPython\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\SeaPython\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\SeaPython\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\SeaPython\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\SeaPython\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\SeaPython\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\SeaPython\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\SeaPython\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\SeaPython\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\SeaPython\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'D:\\SeaPython\\Lib\\html\\parser.py', 'PYMODULE'),
  ('http', 'D:\\SeaPython\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\SeaPython\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\SeaPython\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\SeaPython\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\SeaPython\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'D:\\SeaPython\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'D:\\SeaPython\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\SeaPython\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\SeaPython\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\SeaPython\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\SeaPython\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\SeaPython\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\SeaPython\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\SeaPython\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\SeaPython\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\SeaPython\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\SeaPython\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\SeaPython\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\SeaPython\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\SeaPython\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\SeaPython\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\SeaPython\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\SeaPython\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\SeaPython\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('llvmlite',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\__init__.py',
   'PYMODULE'),
  ('llvmlite._version',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\_version.py',
   'PYMODULE'),
  ('llvmlite.binding',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\__init__.py',
   'PYMODULE'),
  ('llvmlite.binding.analysis',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\analysis.py',
   'PYMODULE'),
  ('llvmlite.binding.common',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\common.py',
   'PYMODULE'),
  ('llvmlite.binding.context',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\context.py',
   'PYMODULE'),
  ('llvmlite.binding.dylib',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\dylib.py',
   'PYMODULE'),
  ('llvmlite.binding.executionengine',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\executionengine.py',
   'PYMODULE'),
  ('llvmlite.binding.ffi',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\ffi.py',
   'PYMODULE'),
  ('llvmlite.binding.initfini',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\initfini.py',
   'PYMODULE'),
  ('llvmlite.binding.linker',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\linker.py',
   'PYMODULE'),
  ('llvmlite.binding.module',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\module.py',
   'PYMODULE'),
  ('llvmlite.binding.newpassmanagers',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\newpassmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.object_file',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\object_file.py',
   'PYMODULE'),
  ('llvmlite.binding.options',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\options.py',
   'PYMODULE'),
  ('llvmlite.binding.orcjit',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\orcjit.py',
   'PYMODULE'),
  ('llvmlite.binding.passmanagers',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\passmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.targets',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\targets.py',
   'PYMODULE'),
  ('llvmlite.binding.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\transforms.py',
   'PYMODULE'),
  ('llvmlite.binding.typeref',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\typeref.py',
   'PYMODULE'),
  ('llvmlite.binding.value',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\value.py',
   'PYMODULE'),
  ('llvmlite.ir',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\__init__.py',
   'PYMODULE'),
  ('llvmlite.ir._utils',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\_utils.py',
   'PYMODULE'),
  ('llvmlite.ir.builder',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\builder.py',
   'PYMODULE'),
  ('llvmlite.ir.context',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\context.py',
   'PYMODULE'),
  ('llvmlite.ir.instructions',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\instructions.py',
   'PYMODULE'),
  ('llvmlite.ir.module',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\module.py',
   'PYMODULE'),
  ('llvmlite.ir.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\transforms.py',
   'PYMODULE'),
  ('llvmlite.ir.types',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\types.py',
   'PYMODULE'),
  ('llvmlite.ir.values',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\values.py',
   'PYMODULE'),
  ('llvmlite.utils',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\utils.py',
   'PYMODULE'),
  ('logging', 'D:\\SeaPython\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lxml', 'D:\\SeaPython\\Lib\\site-packages\\lxml\\__init__.py', 'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'D:\\SeaPython\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\SeaPython\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\SeaPython\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\SeaPython\\Lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'D:\\SeaPython\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'D:\\SeaPython\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'D:\\SeaPython\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\SeaPython\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\SeaPython\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\SeaPython\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\SeaPython\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\SeaPython\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\SeaPython\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\SeaPython\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\SeaPython\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\SeaPython\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\SeaPython\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\SeaPython\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\SeaPython\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\SeaPython\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\SeaPython\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\SeaPython\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\SeaPython\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\SeaPython\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\SeaPython\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\SeaPython\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\SeaPython\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\SeaPython\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numba',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\__init__.py',
   'PYMODULE'),
  ('numba._version',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\_version.py',
   'PYMODULE'),
  ('numba.cext',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cext\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle_fast',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('numba.core',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\__init__.py',
   'PYMODULE'),
  ('numba.core.analysis',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\analysis.py',
   'PYMODULE'),
  ('numba.core.annotations',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\annotations\\__init__.py',
   'PYMODULE'),
  ('numba.core.annotations.pretty_annotate',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\annotations\\pretty_annotate.py',
   'PYMODULE'),
  ('numba.core.annotations.type_annotations',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\annotations\\type_annotations.py',
   'PYMODULE'),
  ('numba.core.base',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\base.py',
   'PYMODULE'),
  ('numba.core.boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\boxing.py',
   'PYMODULE'),
  ('numba.core.bytecode',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\bytecode.py',
   'PYMODULE'),
  ('numba.core.byteflow',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\byteflow.py',
   'PYMODULE'),
  ('numba.core.caching',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\caching.py',
   'PYMODULE'),
  ('numba.core.callconv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\callconv.py',
   'PYMODULE'),
  ('numba.core.callwrapper',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\callwrapper.py',
   'PYMODULE'),
  ('numba.core.ccallback',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ccallback.py',
   'PYMODULE'),
  ('numba.core.cgutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\cgutils.py',
   'PYMODULE'),
  ('numba.core.codegen',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\codegen.py',
   'PYMODULE'),
  ('numba.core.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\compiler.py',
   'PYMODULE'),
  ('numba.core.compiler_lock',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\compiler_lock.py',
   'PYMODULE'),
  ('numba.core.compiler_machinery',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\compiler_machinery.py',
   'PYMODULE'),
  ('numba.core.config',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\config.py',
   'PYMODULE'),
  ('numba.core.consts',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\consts.py',
   'PYMODULE'),
  ('numba.core.controlflow',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\controlflow.py',
   'PYMODULE'),
  ('numba.core.cpu',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\cpu.py',
   'PYMODULE'),
  ('numba.core.cpu_options',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\cpu_options.py',
   'PYMODULE'),
  ('numba.core.datamodel',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\__init__.py',
   'PYMODULE'),
  ('numba.core.datamodel.manager',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\manager.py',
   'PYMODULE'),
  ('numba.core.datamodel.models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\models.py',
   'PYMODULE'),
  ('numba.core.datamodel.new_models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\new_models.py',
   'PYMODULE'),
  ('numba.core.datamodel.old_models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\old_models.py',
   'PYMODULE'),
  ('numba.core.datamodel.packer',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\packer.py',
   'PYMODULE'),
  ('numba.core.datamodel.registry',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\registry.py',
   'PYMODULE'),
  ('numba.core.debuginfo',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\debuginfo.py',
   'PYMODULE'),
  ('numba.core.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\decorators.py',
   'PYMODULE'),
  ('numba.core.descriptors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\descriptors.py',
   'PYMODULE'),
  ('numba.core.dispatcher',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\dispatcher.py',
   'PYMODULE'),
  ('numba.core.entrypoints',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\entrypoints.py',
   'PYMODULE'),
  ('numba.core.environment',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\environment.py',
   'PYMODULE'),
  ('numba.core.errors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\errors.py',
   'PYMODULE'),
  ('numba.core.event',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\event.py',
   'PYMODULE'),
  ('numba.core.extending',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\extending.py',
   'PYMODULE'),
  ('numba.core.externals',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\externals.py',
   'PYMODULE'),
  ('numba.core.fastmathpass',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\fastmathpass.py',
   'PYMODULE'),
  ('numba.core.funcdesc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\funcdesc.py',
   'PYMODULE'),
  ('numba.core.generators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\generators.py',
   'PYMODULE'),
  ('numba.core.imputils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\imputils.py',
   'PYMODULE'),
  ('numba.core.inline_closurecall',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\inline_closurecall.py',
   'PYMODULE'),
  ('numba.core.interpreter',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\interpreter.py',
   'PYMODULE'),
  ('numba.core.intrinsics',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\intrinsics.py',
   'PYMODULE'),
  ('numba.core.ir',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ir.py',
   'PYMODULE'),
  ('numba.core.ir_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ir_utils.py',
   'PYMODULE'),
  ('numba.core.itanium_mangler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\itanium_mangler.py',
   'PYMODULE'),
  ('numba.core.llvm_bindings',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\llvm_bindings.py',
   'PYMODULE'),
  ('numba.core.lowering',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\lowering.py',
   'PYMODULE'),
  ('numba.core.new_boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\new_boxing.py',
   'PYMODULE'),
  ('numba.core.object_mode_passes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\object_mode_passes.py',
   'PYMODULE'),
  ('numba.core.old_boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\old_boxing.py',
   'PYMODULE'),
  ('numba.core.optional',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\optional.py',
   'PYMODULE'),
  ('numba.core.options',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\options.py',
   'PYMODULE'),
  ('numba.core.postproc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\postproc.py',
   'PYMODULE'),
  ('numba.core.pylowering',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\pylowering.py',
   'PYMODULE'),
  ('numba.core.pythonapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\pythonapi.py',
   'PYMODULE'),
  ('numba.core.registry',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\registry.py',
   'PYMODULE'),
  ('numba.core.removerefctpass',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\removerefctpass.py',
   'PYMODULE'),
  ('numba.core.rewrites',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\__init__.py',
   'PYMODULE'),
  ('numba.core.rewrites.ir_print',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\ir_print.py',
   'PYMODULE'),
  ('numba.core.rewrites.registry',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\registry.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_binop',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\static_binop.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_getitem',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\static_getitem.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_raise',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\static_raise.py',
   'PYMODULE'),
  ('numba.core.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\__init__.py',
   'PYMODULE'),
  ('numba.core.runtime.context',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\context.py',
   'PYMODULE'),
  ('numba.core.runtime.nrt',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\nrt.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtdynmod',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\nrtdynmod.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtopt',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\nrtopt.py',
   'PYMODULE'),
  ('numba.core.serialize',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\serialize.py',
   'PYMODULE'),
  ('numba.core.sigutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\sigutils.py',
   'PYMODULE'),
  ('numba.core.ssa',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ssa.py',
   'PYMODULE'),
  ('numba.core.target_extension',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\target_extension.py',
   'PYMODULE'),
  ('numba.core.targetconfig',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\targetconfig.py',
   'PYMODULE'),
  ('numba.core.tracing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\tracing.py',
   'PYMODULE'),
  ('numba.core.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\transforms.py',
   'PYMODULE'),
  ('numba.core.typeconv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\__init__.py',
   'PYMODULE'),
  ('numba.core.typeconv.castgraph',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\castgraph.py',
   'PYMODULE'),
  ('numba.core.typeconv.rules',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\rules.py',
   'PYMODULE'),
  ('numba.core.typeconv.typeconv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\typeconv.py',
   'PYMODULE'),
  ('numba.core.typed_passes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typed_passes.py',
   'PYMODULE'),
  ('numba.core.typeinfer',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeinfer.py',
   'PYMODULE'),
  ('numba.core.types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.abstract',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\abstract.py',
   'PYMODULE'),
  ('numba.core.types.common',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\common.py',
   'PYMODULE'),
  ('numba.core.types.containers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\containers.py',
   'PYMODULE'),
  ('numba.core.types.function_type',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\function_type.py',
   'PYMODULE'),
  ('numba.core.types.functions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\functions.py',
   'PYMODULE'),
  ('numba.core.types.iterators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\iterators.py',
   'PYMODULE'),
  ('numba.core.types.misc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\misc.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.machine_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\machine_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.numpy_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\numpy_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.python_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\python_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\scalars.py',
   'PYMODULE'),
  ('numba.core.types.npytypes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\npytypes.py',
   'PYMODULE'),
  ('numba.core.types.old_scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\old_scalars.py',
   'PYMODULE'),
  ('numba.core.types.scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\scalars.py',
   'PYMODULE'),
  ('numba.core.typing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\__init__.py',
   'PYMODULE'),
  ('numba.core.typing.arraydecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\arraydecl.py',
   'PYMODULE'),
  ('numba.core.typing.asnumbatype',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\asnumbatype.py',
   'PYMODULE'),
  ('numba.core.typing.bufproto',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\bufproto.py',
   'PYMODULE'),
  ('numba.core.typing.builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\builtins.py',
   'PYMODULE'),
  ('numba.core.typing.cffi_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\cffi_utils.py',
   'PYMODULE'),
  ('numba.core.typing.cmathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.collections',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\collections.py',
   'PYMODULE'),
  ('numba.core.typing.context',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\context.py',
   'PYMODULE'),
  ('numba.core.typing.ctypes_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\ctypes_utils.py',
   'PYMODULE'),
  ('numba.core.typing.dictdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\dictdecl.py',
   'PYMODULE'),
  ('numba.core.typing.enumdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\enumdecl.py',
   'PYMODULE'),
  ('numba.core.typing.listdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\listdecl.py',
   'PYMODULE'),
  ('numba.core.typing.mathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\new_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.new_cmathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\new_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_mathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\new_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.npdatetime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\npdatetime.py',
   'PYMODULE'),
  ('numba.core.typing.npydecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\npydecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\old_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.old_cmathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\old_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_mathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\old_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.setdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\setdecl.py',
   'PYMODULE'),
  ('numba.core.typing.templates',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\templates.py',
   'PYMODULE'),
  ('numba.core.typing.typeof',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\typeof.py',
   'PYMODULE'),
  ('numba.core.unsafe',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.core.unsafe.bytes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\unsafe\\bytes.py',
   'PYMODULE'),
  ('numba.core.unsafe.eh',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\unsafe\\eh.py',
   'PYMODULE'),
  ('numba.core.untyped_passes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\untyped_passes.py',
   'PYMODULE'),
  ('numba.core.utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\utils.py',
   'PYMODULE'),
  ('numba.core.withcontexts',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\withcontexts.py',
   'PYMODULE'),
  ('numba.cpython',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\__init__.py',
   'PYMODULE'),
  ('numba.cpython.builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\builtins.py',
   'PYMODULE'),
  ('numba.cpython.charseq',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\charseq.py',
   'PYMODULE'),
  ('numba.cpython.cmathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\cmathimpl.py',
   'PYMODULE'),
  ('numba.cpython.enumimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\enumimpl.py',
   'PYMODULE'),
  ('numba.cpython.hashing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\hashing.py',
   'PYMODULE'),
  ('numba.cpython.heapq',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\heapq.py',
   'PYMODULE'),
  ('numba.cpython.iterators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\iterators.py',
   'PYMODULE'),
  ('numba.cpython.listobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\listobj.py',
   'PYMODULE'),
  ('numba.cpython.mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.new_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_builtins.py',
   'PYMODULE'),
  ('numba.cpython.new_hashing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_hashing.py',
   'PYMODULE'),
  ('numba.cpython.new_mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.new_numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_numbers.py',
   'PYMODULE'),
  ('numba.cpython.new_tupleobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.old_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_builtins.py',
   'PYMODULE'),
  ('numba.cpython.old_hashing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_hashing.py',
   'PYMODULE'),
  ('numba.cpython.old_mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.old_numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_numbers.py',
   'PYMODULE'),
  ('numba.cpython.old_tupleobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.printimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\printimpl.py',
   'PYMODULE'),
  ('numba.cpython.randomimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\randomimpl.py',
   'PYMODULE'),
  ('numba.cpython.rangeobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\rangeobj.py',
   'PYMODULE'),
  ('numba.cpython.setobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\setobj.py',
   'PYMODULE'),
  ('numba.cpython.slicing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\slicing.py',
   'PYMODULE'),
  ('numba.cpython.tupleobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.unicode',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unicode.py',
   'PYMODULE'),
  ('numba.cpython.unicode_support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unicode_support.py',
   'PYMODULE'),
  ('numba.cpython.unsafe',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unsafe\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.tuple',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unsafe\\tuple.py',
   'PYMODULE'),
  ('numba.cuda',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.api',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\api.py',
   'PYMODULE'),
  ('numba.cuda.api_util',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\api_util.py',
   'PYMODULE'),
  ('numba.cuda.args',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\args.py',
   'PYMODULE'),
  ('numba.cuda.cg',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cg.py',
   'PYMODULE'),
  ('numba.cuda.codegen',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\codegen.py',
   'PYMODULE'),
  ('numba.cuda.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.cuda_paths',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cuda_paths.py',
   'PYMODULE'),
  ('numba.cuda.cudadecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadecl.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devicearray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devices',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.driver',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.drvapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.dummyarray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\dummyarray.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.enums',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\enums.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.error',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.libs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\libs.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvrtc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\nvrtc.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvvm',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.rtapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\rtapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.cudaimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudaimpl.py',
   'PYMODULE'),
  ('numba.cuda.cudamath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudamath.py',
   'PYMODULE'),
  ('numba.cuda.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\decorators.py',
   'PYMODULE'),
  ('numba.cuda.descriptor',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\descriptor.py',
   'PYMODULE'),
  ('numba.cuda.device_init',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\device_init.py',
   'PYMODULE'),
  ('numba.cuda.deviceufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\deviceufunc.py',
   'PYMODULE'),
  ('numba.cuda.dispatcher',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\dispatcher.py',
   'PYMODULE'),
  ('numba.cuda.errors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\errors.py',
   'PYMODULE'),
  ('numba.cuda.extending',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\extending.py',
   'PYMODULE'),
  ('numba.cuda.initialize',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\initialize.py',
   'PYMODULE'),
  ('numba.cuda.intrinsic_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\intrinsic_wrapper.py',
   'PYMODULE'),
  ('numba.cuda.intrinsics',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\intrinsics.py',
   'PYMODULE'),
  ('numba.cuda.kernels',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\kernels\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.kernels.reduction',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\kernels\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.kernels.transpose',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\kernels\\transpose.py',
   'PYMODULE'),
  ('numba.cuda.libdevice',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdevice.py',
   'PYMODULE'),
  ('numba.cuda.libdevicedecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdevicedecl.py',
   'PYMODULE'),
  ('numba.cuda.libdevicefuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdevicefuncs.py',
   'PYMODULE'),
  ('numba.cuda.libdeviceimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdeviceimpl.py',
   'PYMODULE'),
  ('numba.cuda.mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\mathimpl.py',
   'PYMODULE'),
  ('numba.cuda.models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\models.py',
   'PYMODULE'),
  ('numba.cuda.nvvmutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\nvvmutils.py',
   'PYMODULE'),
  ('numba.cuda.printimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\printimpl.py',
   'PYMODULE'),
  ('numba.cuda.simulator',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.api',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\api.py',
   'PYMODULE'),
  ('numba.cuda.simulator.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devicearray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devices',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.driver',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.drvapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.error',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.nvvm',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernel',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\kernel.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernelapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\kernelapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.reduction',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.simulator.vector_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.simulator_init',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator_init.py',
   'PYMODULE'),
  ('numba.cuda.stubs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\stubs.py',
   'PYMODULE'),
  ('numba.cuda.target',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\target.py',
   'PYMODULE'),
  ('numba.cuda.testing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\testing.py',
   'PYMODULE'),
  ('numba.cuda.types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\types.py',
   'PYMODULE'),
  ('numba.cuda.ufuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\ufuncs.py',
   'PYMODULE'),
  ('numba.cuda.vector_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.vectorizers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\vectorizers.py',
   'PYMODULE'),
  ('numba.experimental',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.function_type',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\function_type.py',
   'PYMODULE'),
  ('numba.experimental.jitclass',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.base',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\base.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\boxing.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\decorators.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.overloads',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\overloads.py',
   'PYMODULE'),
  ('numba.extending',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\extending.py',
   'PYMODULE'),
  ('numba.misc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\__init__.py',
   'PYMODULE'),
  ('numba.misc.appdirs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\appdirs.py',
   'PYMODULE'),
  ('numba.misc.cffiimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\cffiimpl.py',
   'PYMODULE'),
  ('numba.misc.coverage_support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\coverage_support.py',
   'PYMODULE'),
  ('numba.misc.dump_style',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\dump_style.py',
   'PYMODULE'),
  ('numba.misc.findlib',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\findlib.py',
   'PYMODULE'),
  ('numba.misc.firstlinefinder',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\firstlinefinder.py',
   'PYMODULE'),
  ('numba.misc.gdb_hook',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\gdb_hook.py',
   'PYMODULE'),
  ('numba.misc.init_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\init_utils.py',
   'PYMODULE'),
  ('numba.misc.inspection',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\inspection.py',
   'PYMODULE'),
  ('numba.misc.literal',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\literal.py',
   'PYMODULE'),
  ('numba.misc.llvm_pass_timings',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\llvm_pass_timings.py',
   'PYMODULE'),
  ('numba.misc.mergesort',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\mergesort.py',
   'PYMODULE'),
  ('numba.misc.quicksort',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\quicksort.py',
   'PYMODULE'),
  ('numba.misc.special',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\special.py',
   'PYMODULE'),
  ('numba.np',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\__init__.py',
   'PYMODULE'),
  ('numba.np.arraymath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\arraymath.py',
   'PYMODULE'),
  ('numba.np.arrayobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\arrayobj.py',
   'PYMODULE'),
  ('numba.np.linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\linalg.py',
   'PYMODULE'),
  ('numba.np.math',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\__init__.py',
   'PYMODULE'),
  ('numba.np.math.cmathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\cmathimpl.py',
   'PYMODULE'),
  ('numba.np.math.mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\mathimpl.py',
   'PYMODULE'),
  ('numba.np.math.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\numbers.py',
   'PYMODULE'),
  ('numba.np.new_arraymath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\new_arraymath.py',
   'PYMODULE'),
  ('numba.np.npdatetime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npdatetime.py',
   'PYMODULE'),
  ('numba.np.npdatetime_helpers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npdatetime_helpers.py',
   'PYMODULE'),
  ('numba.np.npyfuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npyfuncs.py',
   'PYMODULE'),
  ('numba.np.npyimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npyimpl.py',
   'PYMODULE'),
  ('numba.np.numpy_support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\numpy_support.py',
   'PYMODULE'),
  ('numba.np.old_arraymath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\old_arraymath.py',
   'PYMODULE'),
  ('numba.np.polynomial',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_core',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\polynomial\\polynomial_core.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_functions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\polynomial\\polynomial_functions.py',
   'PYMODULE'),
  ('numba.np.random',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\__init__.py',
   'PYMODULE'),
  ('numba.np.random._constants',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\_constants.py',
   'PYMODULE'),
  ('numba.np.random.distributions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\distributions.py',
   'PYMODULE'),
  ('numba.np.random.generator_core',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\generator_core.py',
   'PYMODULE'),
  ('numba.np.random.generator_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\generator_methods.py',
   'PYMODULE'),
  ('numba.np.random.new_distributions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\new_distributions.py',
   'PYMODULE'),
  ('numba.np.random.new_random_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\new_random_methods.py',
   'PYMODULE'),
  ('numba.np.random.old_distributions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\old_distributions.py',
   'PYMODULE'),
  ('numba.np.random.old_random_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\old_random_methods.py',
   'PYMODULE'),
  ('numba.np.random.random_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\random_methods.py',
   'PYMODULE'),
  ('numba.np.ufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\__init__.py',
   'PYMODULE'),
  ('numba.np.ufunc.array_exprs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\array_exprs.py',
   'PYMODULE'),
  ('numba.np.ufunc.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\decorators.py',
   'PYMODULE'),
  ('numba.np.ufunc.dufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\dufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.gufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\gufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.parallel',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\parallel.py',
   'PYMODULE'),
  ('numba.np.ufunc.sigparse',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\sigparse.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufunc_base',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\ufunc_base.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufuncbuilder',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\ufuncbuilder.py',
   'PYMODULE'),
  ('numba.np.ufunc.wrappers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\wrappers.py',
   'PYMODULE'),
  ('numba.np.ufunc_db',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc_db.py',
   'PYMODULE'),
  ('numba.np.unsafe',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.np.unsafe.ndarray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\unsafe\\ndarray.py',
   'PYMODULE'),
  ('numba.parfors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\__init__.py',
   'PYMODULE'),
  ('numba.parfors.array_analysis',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\array_analysis.py',
   'PYMODULE'),
  ('numba.parfors.parfor',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\parfor.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\parfor_lowering.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\parfor_lowering_utils.py',
   'PYMODULE'),
  ('numba.pycc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\__init__.py',
   'PYMODULE'),
  ('numba.pycc.cc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\cc.py',
   'PYMODULE'),
  ('numba.pycc.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\compiler.py',
   'PYMODULE'),
  ('numba.pycc.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\decorators.py',
   'PYMODULE'),
  ('numba.pycc.llvm_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\llvm_types.py',
   'PYMODULE'),
  ('numba.pycc.platform',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\platform.py',
   'PYMODULE'),
  ('numba.runtests',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\runtests.py',
   'PYMODULE'),
  ('numba.stencils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\stencils\\__init__.py',
   'PYMODULE'),
  ('numba.stencils.stencil',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\stencils\\stencil.py',
   'PYMODULE'),
  ('numba.stencils.stencilparfor',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\stencils\\stencilparfor.py',
   'PYMODULE'),
  ('numba.testing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\__init__.py',
   'PYMODULE'),
  ('numba.testing._runtests',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\_runtests.py',
   'PYMODULE'),
  ('numba.testing.loader',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\loader.py',
   'PYMODULE'),
  ('numba.testing.main',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\main.py',
   'PYMODULE'),
  ('numba.tests',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\tests\\__init__.py',
   'PYMODULE'),
  ('numba.tests.support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\tests\\support.py',
   'PYMODULE'),
  ('numba.typed',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\__init__.py',
   'PYMODULE'),
  ('numba.typed.dictimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\dictimpl.py',
   'PYMODULE'),
  ('numba.typed.dictobject',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\dictobject.py',
   'PYMODULE'),
  ('numba.typed.listobject',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\listobject.py',
   'PYMODULE'),
  ('numba.typed.typeddict',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\typeddict.py',
   'PYMODULE'),
  ('numba.typed.typedlist',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\typedlist.py',
   'PYMODULE'),
  ('numba.typed.typedobjectutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\typedobjectutils.py',
   'PYMODULE'),
  ('numba.types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\types\\__init__.py',
   'PYMODULE'),
  ('numbers', 'D:\\SeaPython\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\SeaPython\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\SeaPython\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\SeaPython\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\SeaPython\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\SeaPython\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'D:\\SeaPython\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\SeaPython\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'D:\\SeaPython\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\SeaPython\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\SeaPython\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\SeaPython\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'D:\\SeaPython\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\SeaPython\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'D:\\SeaPython\\Lib\\profile.py', 'PYMODULE'),
  ('pstats', 'D:\\SeaPython\\Lib\\pstats.py', 'PYMODULE'),
  ('psutil',
   'D:\\SeaPython\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\SeaPython\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\SeaPython\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\SeaPython\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\SeaPython\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\SeaPython\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\SeaPython\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz', 'D:\\SeaPython\\Lib\\site-packages\\pytz\\__init__.py', 'PYMODULE'),
  ('pytz.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy', 'D:\\SeaPython\\Lib\\site-packages\\pytz\\lazy.py', 'PYMODULE'),
  ('pytz.tzfile',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\SeaPython\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\SeaPython\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\SeaPython\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\SeaPython\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\SeaPython\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\SeaPython\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\SeaPython\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.build_ext',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\SeaPython\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\SeaPython\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\SeaPython\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\SeaPython\\Lib\\site.py', 'PYMODULE'),
  ('six', 'D:\\SeaPython\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'D:\\SeaPython\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\SeaPython\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\SeaPython\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('soupsieve',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\SeaPython\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'D:\\SeaPython\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\SeaPython\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\SeaPython\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'D:\\SeaPython\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\SeaPython\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\SeaPython\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\SeaPython\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\SeaPython\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\SeaPython\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'D:\\SeaPython\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\SeaPython\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\SeaPython\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\SeaPython\\Lib\\threading.py', 'PYMODULE'),
  ('timeit', 'D:\\SeaPython\\Lib\\timeit.py', 'PYMODULE'),
  ('tkinter', 'D:\\SeaPython\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\SeaPython\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\SeaPython\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog', 'D:\\SeaPython\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\SeaPython\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\SeaPython\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\SeaPython\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\SeaPython\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\SeaPython\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\SeaPython\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\SeaPython\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\SeaPython\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\SeaPython\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\SeaPython\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\SeaPython\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tqdm', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\__init__.py', 'PYMODULE'),
  ('tqdm._dist_ver',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\cli.py', 'PYMODULE'),
  ('tqdm.gui', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\gui.py', 'PYMODULE'),
  ('tqdm.notebook',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\std.py', 'PYMODULE'),
  ('tqdm.utils',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\SeaPython\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\SeaPython\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\SeaPython\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\SeaPython\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\SeaPython\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\SeaPython\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\SeaPython\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\SeaPython\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\SeaPython\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\SeaPython\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\SeaPython\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'D:\\SeaPython\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\SeaPython\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\SeaPython\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\SeaPython\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\SeaPython\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\SeaPython\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\SeaPython\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\SeaPython\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\SeaPython\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\SeaPython\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\SeaPython\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\SeaPython\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xlsxwriter',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.color',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('xml', 'D:\\SeaPython\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\SeaPython\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\SeaPython\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'D:\\SeaPython\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\SeaPython\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\SeaPython\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'D:\\SeaPython\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\SeaPython\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\SeaPython\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'D:\\SeaPython\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\SeaPython\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\SeaPython\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\SeaPython\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\SeaPython\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\SeaPython\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\SeaPython\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\SeaPython\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\SeaPython\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\SeaPython\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\SeaPython\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\SeaPython\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\SeaPython\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\SeaPython\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\SeaPython\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\SeaPython\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\SeaPython\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\SeaPython\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\SeaPython\\Lib\\zipimport.py', 'PYMODULE')])
