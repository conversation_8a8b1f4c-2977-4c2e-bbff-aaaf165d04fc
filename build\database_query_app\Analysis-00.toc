(['C:\\Users\\<USER>\\Desktop\\test\\database_query_app.py'],
 ['C:\\Users\\<USER>\\Desktop\\test'],
 [],
 [('D:\\SeaPython\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\SeaPython\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\SeaPython\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('database_query_app',
   'C:\\Users\\<USER>\\Desktop\\test\\database_query_app.py',
   'PYSOURCE')],
 [('_distutils_hack',
   'D:\\SeaPython\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\SeaPython\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\SeaPython\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\SeaPython\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\SeaPython\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\SeaPython\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\SeaPython\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'D:\\SeaPython\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\SeaPython\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\SeaPython\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\SeaPython\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\SeaPython\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\SeaPython\\Lib\\email\\generator.py', 'PYMODULE'),
  ('copy', 'D:\\SeaPython\\Lib\\copy.py', 'PYMODULE'),
  ('random', 'D:\\SeaPython\\Lib\\random.py', 'PYMODULE'),
  ('argparse', 'D:\\SeaPython\\Lib\\argparse.py', 'PYMODULE'),
  ('shutil', 'D:\\SeaPython\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\SeaPython\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\SeaPython\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\SeaPython\\Lib\\_compression.py', 'PYMODULE'),
  ('struct', 'D:\\SeaPython\\Lib\\struct.py', 'PYMODULE'),
  ('lzma', 'D:\\SeaPython\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\SeaPython\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\SeaPython\\Lib\\fnmatch.py', 'PYMODULE'),
  ('gettext', 'D:\\SeaPython\\Lib\\gettext.py', 'PYMODULE'),
  ('statistics', 'D:\\SeaPython\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\SeaPython\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\SeaPython\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\SeaPython\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\SeaPython\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\SeaPython\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\SeaPython\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\SeaPython\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\SeaPython\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\SeaPython\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\SeaPython\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\SeaPython\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('bisect', 'D:\\SeaPython\\Lib\\bisect.py', 'PYMODULE'),
  ('_strptime', 'D:\\SeaPython\\Lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'D:\\SeaPython\\Lib\\calendar.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\SeaPython\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\SeaPython\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\SeaPython\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\SeaPython\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\SeaPython\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\SeaPython\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\SeaPython\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\SeaPython\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\SeaPython\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\SeaPython\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'D:\\SeaPython\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\SeaPython\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\SeaPython\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\SeaPython\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\SeaPython\\Lib\\ipaddress.py', 'PYMODULE'),
  ('quopri', 'D:\\SeaPython\\Lib\\quopri.py', 'PYMODULE'),
  ('typing', 'D:\\SeaPython\\Lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'D:\\SeaPython\\Lib\\contextlib.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('textwrap', 'D:\\SeaPython\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'D:\\SeaPython\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\SeaPython\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\SeaPython\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'D:\\SeaPython\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\SeaPython\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('pathlib', 'D:\\SeaPython\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\SeaPython\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'D:\\SeaPython\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\SeaPython\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('inspect', 'D:\\SeaPython\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'D:\\SeaPython\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'D:\\SeaPython\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\SeaPython\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\SeaPython\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'D:\\SeaPython\\Lib\\ast.py', 'PYMODULE'),
  ('email', 'D:\\SeaPython\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\SeaPython\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\SeaPython\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('json', 'D:\\SeaPython\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\SeaPython\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\SeaPython\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\SeaPython\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('__future__', 'D:\\SeaPython\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\SeaPython\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\SeaPython\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\SeaPython\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\SeaPython\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\SeaPython\\Lib\\tempfile.py', 'PYMODULE'),
  ('tokenize', 'D:\\SeaPython\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\SeaPython\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', 'D:\\SeaPython\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\SeaPython\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib', 'D:\\SeaPython\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('setuptools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\SeaPython\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\SeaPython\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio', 'D:\\SeaPython\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\SeaPython\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\SeaPython\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('subprocess', 'D:\\SeaPython\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\SeaPython\\Lib\\signal.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\SeaPython\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\SeaPython\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\SeaPython\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('ssl', 'D:\\SeaPython\\Lib\\ssl.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\SeaPython\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\SeaPython\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'D:\\SeaPython\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\SeaPython\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\SeaPython\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'D:\\SeaPython\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\SeaPython\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\SeaPython\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'D:\\SeaPython\\Lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\SeaPython\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\SeaPython\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\SeaPython\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\SeaPython\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'D:\\SeaPython\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\SeaPython\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\SeaPython\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing.util',
   'D:\\SeaPython\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\SeaPython\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\SeaPython\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\SeaPython\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\SeaPython\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\SeaPython\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes', 'D:\\SeaPython\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'D:\\SeaPython\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\SeaPython\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\SeaPython\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'D:\\SeaPython\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\SeaPython\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\SeaPython\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\SeaPython\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\SeaPython\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\SeaPython\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\SeaPython\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'D:\\SeaPython\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\SeaPython\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\SeaPython\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\SeaPython\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\SeaPython\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\SeaPython\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\SeaPython\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\SeaPython\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\SeaPython\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'D:\\SeaPython\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\SeaPython\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\SeaPython\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'D:\\SeaPython\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'D:\\SeaPython\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\SeaPython\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\SeaPython\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\SeaPython\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\SeaPython\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\SeaPython\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\SeaPython\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib.response', 'D:\\SeaPython\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'D:\\SeaPython\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'D:\\SeaPython\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'D:\\SeaPython\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\SeaPython\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\SeaPython\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'D:\\SeaPython\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\SeaPython\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\SeaPython\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'D:\\SeaPython\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\SeaPython\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\SeaPython\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts', 'D:\\SeaPython\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\SeaPython\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\SeaPython\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\SeaPython\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'D:\\SeaPython\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\SeaPython\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'D:\\SeaPython\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'D:\\SeaPython\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\SeaPython\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\SeaPython\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\SeaPython\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\SeaPython\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\SeaPython\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\SeaPython\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\SeaPython\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('more_itertools',
   'D:\\SeaPython\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'D:\\SeaPython\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('more_itertools.more',
   'D:\\SeaPython\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'D:\\SeaPython\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'D:\\SeaPython\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('unittest.mock', 'D:\\SeaPython\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'D:\\SeaPython\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\SeaPython\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'D:\\SeaPython\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'D:\\SeaPython\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\SeaPython\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\SeaPython\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\SeaPython\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'D:\\SeaPython\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\SeaPython\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'D:\\SeaPython\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'D:\\SeaPython\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'D:\\SeaPython\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('sysconfig', 'D:\\SeaPython\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\SeaPython\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('site', 'D:\\SeaPython\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'D:\\SeaPython\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\SeaPython\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'D:\\SeaPython\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'D:\\SeaPython\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\SeaPython\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\SeaPython\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'D:\\SeaPython\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'D:\\SeaPython\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'D:\\SeaPython\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands', 'D:\\SeaPython\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.pager', 'D:\\SeaPython\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'D:\\SeaPython\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\SeaPython\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'D:\\SeaPython\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\SeaPython\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'D:\\SeaPython\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'D:\\SeaPython\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', 'D:\\SeaPython\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('code', 'D:\\SeaPython\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\SeaPython\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'D:\\SeaPython\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\SeaPython\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\SeaPython\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\SeaPython\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\SeaPython\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\SeaPython\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'D:\\SeaPython\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('_pyrepl.readline', 'D:\\SeaPython\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\SeaPython\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\SeaPython\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\SeaPython\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'D:\\SeaPython\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\SeaPython\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'D:\\SeaPython\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'D:\\SeaPython\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\SeaPython\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\SeaPython\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\SeaPython\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\SeaPython\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'D:\\SeaPython\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'D:\\SeaPython\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\SeaPython\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('plistlib', 'D:\\SeaPython\\Lib\\plistlib.py', 'PYMODULE'),
  ('packaging._musllinux',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\SeaPython\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'D:\\SeaPython\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\SeaPython\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\SeaPython\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\SeaPython\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.build_ext',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\SeaPython\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('stringprep', 'D:\\SeaPython\\Lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'D:\\SeaPython\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\SeaPython\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('csv', 'D:\\SeaPython\\Lib\\csv.py', 'PYMODULE'),
  ('xlsxwriter',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE'),
  ('xlsxwriter.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE'),
  ('xlsxwriter.utility',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE'),
  ('xlsxwriter.color',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE'),
  ('xlsxwriter.sharedstrings',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE'),
  ('xlsxwriter.packager',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE'),
  ('xlsxwriter.vml',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE'),
  ('xlsxwriter.theme',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE'),
  ('xlsxwriter.table',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE'),
  ('xlsxwriter.styles',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_types',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_structure',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value_rel',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE'),
  ('xlsxwriter.rich_value',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE'),
  ('xlsxwriter.relationships',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE'),
  ('xlsxwriter.metadata',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE'),
  ('xlsxwriter.feature_property_bag',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE'),
  ('xlsxwriter.custom',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE'),
  ('xlsxwriter.core',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE'),
  ('xlsxwriter.contenttypes',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE'),
  ('xlsxwriter.comments',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE'),
  ('xlsxwriter.app',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE'),
  ('xlsxwriter.format',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE'),
  ('xlsxwriter.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE'),
  ('xlsxwriter.chartsheet',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE'),
  ('xlsxwriter.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE'),
  ('xlsxwriter.shape',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE'),
  ('xlsxwriter.url',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE'),
  ('xlsxwriter.chart_stock',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE'),
  ('xlsxwriter.chart_scatter',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE'),
  ('xlsxwriter.chart_radar',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_line',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE'),
  ('xlsxwriter.chart_doughnut',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE'),
  ('xlsxwriter.chart_column',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE'),
  ('xlsxwriter.chart_bar',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE'),
  ('xlsxwriter.chart_area',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE'),
  ('xlsxwriter.image',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE'),
  ('xlsxwriter.worksheet',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE'),
  ('xlsxwriter.chart_pie',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE'),
  ('xlsxwriter.chart',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE'),
  ('xlsxwriter.xmlwriter',
   'D:\\SeaPython\\Lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\SeaPython\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\SeaPython\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\SeaPython\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\SeaPython\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\SeaPython\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\SeaPython\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\SeaPython\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'D:\\SeaPython\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('lxml', 'D:\\SeaPython\\Lib\\site-packages\\lxml\\__init__.py', 'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('bs4', 'D:\\SeaPython\\Lib\\site-packages\\bs4\\__init__.py', 'PYMODULE'),
  ('bs4._warnings',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4._typing',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4.filter',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4.css', 'D:\\SeaPython\\Lib\\site-packages\\bs4\\css.py', 'PYMODULE'),
  ('soupsieve',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\SeaPython\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('html.parser', 'D:\\SeaPython\\Lib\\html\\parser.py', 'PYMODULE'),
  ('_markupbase', 'D:\\SeaPython\\Lib\\_markupbase.py', 'PYMODULE'),
  ('bs4.builder',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\SeaPython\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('optparse', 'D:\\SeaPython\\Lib\\optparse.py', 'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('doctest', 'D:\\SeaPython\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\SeaPython\\Lib\\pdb.py', 'PYMODULE'),
  ('bdb', 'D:\\SeaPython\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\SeaPython\\Lib\\cmd.py', 'PYMODULE'),
  ('lxml.cssselect',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput', 'D:\\SeaPython\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'D:\\SeaPython\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\SeaPython\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\SeaPython\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy.testing._private',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\SeaPython\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\SeaPython\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL._util', 'D:\\SeaPython\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._deprecate',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL', 'D:\\SeaPython\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\SeaPython\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('pandas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('six', 'D:\\SeaPython\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('numba',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\__init__.py',
   'PYMODULE'),
  ('numba.np.random.new_random_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\new_random_methods.py',
   'PYMODULE'),
  ('numba.np.random',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\__init__.py',
   'PYMODULE'),
  ('numba.np.random.generator_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\generator_methods.py',
   'PYMODULE'),
  ('numba.np.random.distributions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\distributions.py',
   'PYMODULE'),
  ('numba.core.utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\utils.py',
   'PYMODULE'),
  ('numba.misc.dump_style',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\dump_style.py',
   'PYMODULE'),
  ('numba.misc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\__init__.py',
   'PYMODULE'),
  ('numba.misc.literal',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\literal.py',
   'PYMODULE'),
  ('numba.misc.gdb_hook',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\gdb_hook.py',
   'PYMODULE'),
  ('numba.core.cgutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\cgutils.py',
   'PYMODULE'),
  ('numba.core.datamodel',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\__init__.py',
   'PYMODULE'),
  ('numba.core.datamodel.models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\models.py',
   'PYMODULE'),
  ('numba.core.datamodel.registry',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\registry.py',
   'PYMODULE'),
  ('numba.core.datamodel.packer',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\packer.py',
   'PYMODULE'),
  ('numba.core.datamodel.manager',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\manager.py',
   'PYMODULE'),
  ('numba.core.debuginfo',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\debuginfo.py',
   'PYMODULE'),
  ('llvmlite.ir',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\__init__.py',
   'PYMODULE'),
  ('llvmlite.ir.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\transforms.py',
   'PYMODULE'),
  ('llvmlite.ir.builder',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\builder.py',
   'PYMODULE'),
  ('llvmlite.ir.instructions',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\instructions.py',
   'PYMODULE'),
  ('llvmlite.ir._utils',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\_utils.py',
   'PYMODULE'),
  ('llvmlite.ir.module',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\module.py',
   'PYMODULE'),
  ('llvmlite.ir.context',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\context.py',
   'PYMODULE'),
  ('llvmlite.ir.values',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\values.py',
   'PYMODULE'),
  ('llvmlite.ir.types',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\ir\\types.py',
   'PYMODULE'),
  ('numba.misc.cffiimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\cffiimpl.py',
   'PYMODULE'),
  ('numba.np.arrayobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\arrayobj.py',
   'PYMODULE'),
  ('numba.core.typing.npydecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\npydecl.py',
   'PYMODULE'),
  ('numba.core.typing.templates',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\templates.py',
   'PYMODULE'),
  ('numba.core.inline_closurecall',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\inline_closurecall.py',
   'PYMODULE'),
  ('numba.typed.listobject',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\listobject.py',
   'PYMODULE'),
  ('numba.typed.typedobjectutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\typedobjectutils.py',
   'PYMODULE'),
  ('numba.core.typeconv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\__init__.py',
   'PYMODULE'),
  ('numba.core.typeconv.rules',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\rules.py',
   'PYMODULE'),
  ('numba.core.typeconv.typeconv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\typeconv.py',
   'PYMODULE'),
  ('numba.core.typeconv.castgraph',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\castgraph.py',
   'PYMODULE'),
  ('numba.core.registry',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\registry.py',
   'PYMODULE'),
  ('numba.core.cpu',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\cpu.py',
   'PYMODULE'),
  ('numba.typed.dictobject',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\dictobject.py',
   'PYMODULE'),
  ('numba.np.unsafe',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.base',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\base.py',
   'PYMODULE'),
  ('numba.core.serialize',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\serialize.py',
   'PYMODULE'),
  ('numba.core.typing.asnumbatype',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\asnumbatype.py',
   'PYMODULE'),
  ('numba.core.typing.typeof',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\typeof.py',
   'PYMODULE'),
  ('numba.core.typing.ctypes_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\ctypes_utils.py',
   'PYMODULE'),
  ('numba.core.typing.bufproto',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\bufproto.py',
   'PYMODULE'),
  ('numba.core.typing.cffi_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\cffi_utils.py',
   'PYMODULE'),
  ('cffi', 'D:\\SeaPython\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi.error',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api', 'D:\\SeaPython\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.recompiler',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\SeaPython\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\SeaPython\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\SeaPython\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('numba.cpython.randomimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\randomimpl.py',
   'PYMODULE'),
  ('numba.cpython.printimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\printimpl.py',
   'PYMODULE'),
  ('numba.cpython.mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.cmathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\cmathimpl.py',
   'PYMODULE'),
  ('numba.np.npyimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npyimpl.py',
   'PYMODULE'),
  ('numba.np.ufunc.sigparse',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\sigparse.py',
   'PYMODULE'),
  ('numba.np.npdatetime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npdatetime.py',
   'PYMODULE'),
  ('numba.extending',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\extending.py',
   'PYMODULE'),
  ('numba.np.npyfuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npyfuncs.py',
   'PYMODULE'),
  ('numba.np.math.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unsafe\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.unsafe',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.np.math.mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\mathimpl.py',
   'PYMODULE'),
  ('numba.np.math.cmathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\cmathimpl.py',
   'PYMODULE'),
  ('numba.np.math',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\math\\__init__.py',
   'PYMODULE'),
  ('numba.core.lowering',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\lowering.py',
   'PYMODULE'),
  ('numba.core.pythonapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\pythonapi.py',
   'PYMODULE'),
  ('numba.core.boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\boxing.py',
   'PYMODULE'),
  ('numba.core.unsafe.eh',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\unsafe\\eh.py',
   'PYMODULE'),
  ('numba.core.unsafe',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.misc.coverage_support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\coverage_support.py',
   'PYMODULE'),
  ('numba.misc.firstlinefinder',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\firstlinefinder.py',
   'PYMODULE'),
  ('numba.core.environment',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\environment.py',
   'PYMODULE'),
  ('numba.core.removerefctpass',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\removerefctpass.py',
   'PYMODULE'),
  ('numba.core.generators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\generators.py',
   'PYMODULE'),
  ('numba.core.funcdesc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\funcdesc.py',
   'PYMODULE'),
  ('numba.core.itanium_mangler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\itanium_mangler.py',
   'PYMODULE'),
  ('numba.np.npdatetime_helpers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\npdatetime_helpers.py',
   'PYMODULE'),
  ('numba.experimental.function_type',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\function_type.py',
   'PYMODULE'),
  ('numba.core.ccallback',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ccallback.py',
   'PYMODULE'),
  ('numba.core.caching',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\caching.py',
   'PYMODULE'),
  ('numba.misc.appdirs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\appdirs.py',
   'PYMODULE'),
  ('uuid', 'D:\\SeaPython\\Lib\\uuid.py', 'PYMODULE'),
  ('numba.experimental.jitclass',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.overloads',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\overloads.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\boxing.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\decorators.py',
   'PYMODULE'),
  ('numba.typed.typedlist',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\typedlist.py',
   'PYMODULE'),
  ('numba.typed.dictimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\dictimpl.py',
   'PYMODULE'),
  ('numba.typed.typeddict',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\typeddict.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_functions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\polynomial\\polynomial_functions.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_core',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\polynomial\\polynomial_core.py',
   'PYMODULE'),
  ('numba.np.polynomial',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numba.np.arraymath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\arraymath.py',
   'PYMODULE'),
  ('numba.np.linalg',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\linalg.py',
   'PYMODULE'),
  ('numba.core.optional',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\optional.py',
   'PYMODULE'),
  ('numba.cpython.unicode',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unicode.py',
   'PYMODULE'),
  ('numba.cpython.unicode_support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unicode_support.py',
   'PYMODULE'),
  ('numba.core.unsafe.bytes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\unsafe\\bytes.py',
   'PYMODULE'),
  ('numba.cpython.tupleobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.setobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\setobj.py',
   'PYMODULE'),
  ('numba.cpython.numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.iterators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\iterators.py',
   'PYMODULE'),
  ('numba.cpython.heapq',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\heapq.py',
   'PYMODULE'),
  ('numba.cpython.hashing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\hashing.py',
   'PYMODULE'),
  ('numba.cpython.enumimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\enumimpl.py',
   'PYMODULE'),
  ('numba.cpython.charseq',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\charseq.py',
   'PYMODULE'),
  ('numba.cpython.builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\builtins.py',
   'PYMODULE'),
  ('numba.np.ufunc_db',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc_db.py',
   'PYMODULE'),
  ('numba.core.entrypoints',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\entrypoints.py',
   'PYMODULE'),
  ('numba.core.compiler_lock',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\compiler_lock.py',
   'PYMODULE'),
  ('numba.core.event',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\event.py',
   'PYMODULE'),
  ('numba.core.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\__init__.py',
   'PYMODULE'),
  ('numba.core.runtime.nrt',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\nrt.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtdynmod',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\nrtdynmod.py',
   'PYMODULE'),
  ('numba.core.options',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\options.py',
   'PYMODULE'),
  ('numba.core.intrinsics',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\intrinsics.py',
   'PYMODULE'),
  ('numba.core.fastmathpass',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\fastmathpass.py',
   'PYMODULE'),
  ('numba.core.externals',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\externals.py',
   'PYMODULE'),
  ('numba.core.codegen',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\codegen.py',
   'PYMODULE'),
  ('numba.misc.llvm_pass_timings',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\llvm_pass_timings.py',
   'PYMODULE'),
  ('numba.misc.inspection',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\inspection.py',
   'PYMODULE'),
  ('numba.pycc.platform',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\platform.py',
   'PYMODULE'),
  ('numba.pycc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\__init__.py',
   'PYMODULE'),
  ('numba.pycc.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\decorators.py',
   'PYMODULE'),
  ('numba.pycc.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\compiler.py',
   'PYMODULE'),
  ('numba.core.sigutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\sigutils.py',
   'PYMODULE'),
  ('numba.pycc.cc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\cc.py',
   'PYMODULE'),
  ('numba.pycc.llvm_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\pycc\\llvm_types.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtopt',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\nrtopt.py',
   'PYMODULE'),
  ('numba.core.llvm_bindings',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\llvm_bindings.py',
   'PYMODULE'),
  ('numba.core.callconv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\callconv.py',
   'PYMODULE'),
  ('numba.core.base',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\base.py',
   'PYMODULE'),
  ('numba.core.runtime.context',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\context.py',
   'PYMODULE'),
  ('numba.core.callwrapper',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\callwrapper.py',
   'PYMODULE'),
  ('numba.core.dispatcher',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\dispatcher.py',
   'PYMODULE'),
  ('numba.core.annotations.pretty_annotate',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\annotations\\pretty_annotate.py',
   'PYMODULE'),
  ('numba.core.annotations',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\annotations\\__init__.py',
   'PYMODULE'),
  ('numba.core.annotations.type_annotations',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\annotations\\type_annotations.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   'D:\\SeaPython\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\SeaPython\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\SeaPython\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('numba.core.descriptors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\descriptors.py',
   'PYMODULE'),
  ('numba.core.ssa',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ssa.py',
   'PYMODULE'),
  ('numba.core.bytecode',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\bytecode.py',
   'PYMODULE'),
  ('numba.core.untyped_passes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\untyped_passes.py',
   'PYMODULE'),
  ('numba.core.interpreter',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\interpreter.py',
   'PYMODULE'),
  ('numba.core.byteflow',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\byteflow.py',
   'PYMODULE'),
  ('numba.core.controlflow',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\controlflow.py',
   'PYMODULE'),
  ('numba.core.consts',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\consts.py',
   'PYMODULE'),
  ('numba.core.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\transforms.py',
   'PYMODULE'),
  ('numba.core.compiler_machinery',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\compiler_machinery.py',
   'PYMODULE'),
  ('numba.core.tracing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\tracing.py',
   'PYMODULE'),
  ('numba.stencils.stencil',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\stencils\\stencil.py',
   'PYMODULE'),
  ('numba.stencils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\stencils\\__init__.py',
   'PYMODULE'),
  ('numba.np.unsafe.ndarray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\unsafe\\ndarray.py',
   'PYMODULE'),
  ('numba.core.postproc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\postproc.py',
   'PYMODULE'),
  ('numba.cpython.rangeobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\rangeobj.py',
   'PYMODULE'),
  ('numba.cpython.listobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\listobj.py',
   'PYMODULE'),
  ('numba.parfors.parfor',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\parfor.py',
   'PYMODULE'),
  ('numba.parfors.array_analysis',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\array_analysis.py',
   'PYMODULE'),
  ('numba.core.types.functions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\functions.py',
   'PYMODULE'),
  ('numba.core.types.misc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\misc.py',
   'PYMODULE'),
  ('numba.core.types.common',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\common.py',
   'PYMODULE'),
  ('numba.core.types.iterators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\iterators.py',
   'PYMODULE'),
  ('numba.core.types.abstract',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\abstract.py',
   'PYMODULE'),
  ('numba.stencils.stencilparfor',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\stencils\\stencilparfor.py',
   'PYMODULE'),
  ('numba.parfors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\__init__.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\parfor_lowering.py',
   'PYMODULE'),
  ('numba.np.ufunc.parallel',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\parallel.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufuncbuilder',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\ufuncbuilder.py',
   'PYMODULE'),
  ('numba.np.ufunc.wrappers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\wrappers.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\parfors\\parfor_lowering_utils.py',
   'PYMODULE'),
  ('numba.core.typeinfer',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeinfer.py',
   'PYMODULE'),
  ('numba.core.ir_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ir_utils.py',
   'PYMODULE'),
  ('numba.core.rewrites',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\__init__.py',
   'PYMODULE'),
  ('numba.core.rewrites.ir_print',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\ir_print.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_binop',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\static_binop.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_raise',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\static_raise.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_getitem',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\static_getitem.py',
   'PYMODULE'),
  ('numba.core.rewrites.registry',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\rewrites\\registry.py',
   'PYMODULE'),
  ('numba.core.ir',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\ir.py',
   'PYMODULE'),
  ('numba.core.analysis',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\analysis.py',
   'PYMODULE'),
  ('numba.core.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\compiler.py',
   'PYMODULE'),
  ('numba.core.object_mode_passes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\object_mode_passes.py',
   'PYMODULE'),
  ('numba.core.pylowering',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\pylowering.py',
   'PYMODULE'),
  ('numba.core.typed_passes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typed_passes.py',
   'PYMODULE'),
  ('numba.core.cpu_options',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\cpu_options.py',
   'PYMODULE'),
  ('numba.core.targetconfig',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\targetconfig.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.tuple',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\unsafe\\tuple.py',
   'PYMODULE'),
  ('numba.cpython.slicing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\slicing.py',
   'PYMODULE'),
  ('numba.cpython',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\__init__.py',
   'PYMODULE'),
  ('numba.core.typing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\__init__.py',
   'PYMODULE'),
  ('numba.core.typing.context',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\context.py',
   'PYMODULE'),
  ('numba.core.typing.dictdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\dictdecl.py',
   'PYMODULE'),
  ('numba.core.typing.setdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\setdecl.py',
   'PYMODULE'),
  ('numba.core.typing.mathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.listdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\listdecl.py',
   'PYMODULE'),
  ('numba.core.typing.npdatetime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\npdatetime.py',
   'PYMODULE'),
  ('numba.core.typing.arraydecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\arraydecl.py',
   'PYMODULE'),
  ('numba.core.typing.collections',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\collections.py',
   'PYMODULE'),
  ('numba.core.typing.builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\builtins.py',
   'PYMODULE'),
  ('numba.core.typing.enumdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\enumdecl.py',
   'PYMODULE'),
  ('numba.core.typing.cmathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\cmathdecl.py',
   'PYMODULE'),
  ('numba.core.imputils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\imputils.py',
   'PYMODULE'),
  ('numba.misc.mergesort',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\mergesort.py',
   'PYMODULE'),
  ('numba.misc.quicksort',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\quicksort.py',
   'PYMODULE'),
  ('timeit', 'D:\\SeaPython\\Lib\\timeit.py', 'PYMODULE'),
  ('numba.core.types.containers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\containers.py',
   'PYMODULE'),
  ('numba.np.random.random_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\random_methods.py',
   'PYMODULE'),
  ('numba.np',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\__init__.py',
   'PYMODULE'),
  ('numba.np.random.generator_core',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\generator_core.py',
   'PYMODULE'),
  ('numba.np.random._constants',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\_constants.py',
   'PYMODULE'),
  ('numba.core.extending',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\extending.py',
   'PYMODULE'),
  ('numba.np.random.new_distributions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\new_distributions.py',
   'PYMODULE'),
  ('numba.np.new_arraymath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\new_arraymath.py',
   'PYMODULE'),
  ('numba.cpython.new_tupleobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.new_numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_numbers.py',
   'PYMODULE'),
  ('numba.cpython.new_mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.new_hashing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_hashing.py',
   'PYMODULE'),
  ('numba.cpython.new_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\new_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.new_mathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\new_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_cmathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\new_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\new_builtins.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.numpy_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\numpy_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.machine_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\machine_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.python_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\python_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\new_scalars\\scalars.py',
   'PYMODULE'),
  ('numba.core.new_boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\new_boxing.py',
   'PYMODULE'),
  ('numba.core.datamodel.new_models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\new_models.py',
   'PYMODULE'),
  ('numba.np.random.old_random_methods',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\old_random_methods.py',
   'PYMODULE'),
  ('numba.np.random.old_distributions',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\random\\old_distributions.py',
   'PYMODULE'),
  ('numba.np.old_arraymath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\old_arraymath.py',
   'PYMODULE'),
  ('numba.cpython.old_tupleobj',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.old_numbers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_numbers.py',
   'PYMODULE'),
  ('numba.cpython.old_mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.old_hashing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_hashing.py',
   'PYMODULE'),
  ('numba.cpython.old_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cpython\\old_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.old_mathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\old_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_cmathdecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\old_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_builtins',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typing\\old_builtins.py',
   'PYMODULE'),
  ('numba.core.types.old_scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\old_scalars.py',
   'PYMODULE'),
  ('numba.core.old_boxing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\old_boxing.py',
   'PYMODULE'),
  ('numba.core.datamodel.old_models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\datamodel\\old_models.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle_fast',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('llvmlite.binding',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\__init__.py',
   'PYMODULE'),
  ('llvmlite.binding.orcjit',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\orcjit.py',
   'PYMODULE'),
  ('llvmlite.binding.context',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\context.py',
   'PYMODULE'),
  ('llvmlite.binding.analysis',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\analysis.py',
   'PYMODULE'),
  ('llvmlite.binding.typeref',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\typeref.py',
   'PYMODULE'),
  ('llvmlite.binding.value',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\value.py',
   'PYMODULE'),
  ('llvmlite.binding.common',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\common.py',
   'PYMODULE'),
  ('llvmlite.binding.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\transforms.py',
   'PYMODULE'),
  ('llvmlite.binding.passmanagers',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\passmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.newpassmanagers',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\newpassmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.options',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\options.py',
   'PYMODULE'),
  ('llvmlite.binding.module',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\module.py',
   'PYMODULE'),
  ('llvmlite.binding.linker',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\linker.py',
   'PYMODULE'),
  ('llvmlite.binding.initfini',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\initfini.py',
   'PYMODULE'),
  ('llvmlite.binding.executionengine',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\executionengine.py',
   'PYMODULE'),
  ('llvmlite.binding.object_file',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\object_file.py',
   'PYMODULE'),
  ('llvmlite.binding.targets',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\targets.py',
   'PYMODULE'),
  ('llvmlite.binding.dylib',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\dylib.py',
   'PYMODULE'),
  ('llvmlite.binding.ffi',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\ffi.py',
   'PYMODULE'),
  ('llvmlite.utils',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\utils.py',
   'PYMODULE'),
  ('llvmlite',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\__init__.py',
   'PYMODULE'),
  ('llvmlite._version',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\_version.py',
   'PYMODULE'),
  ('numba.testing._runtests',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\_runtests.py',
   'PYMODULE'),
  ('numba.tests.support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\tests\\support.py',
   'PYMODULE'),
  ('numba.tests',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\tests\\__init__.py',
   'PYMODULE'),
  ('numba.testing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\__init__.py',
   'PYMODULE'),
  ('numba.testing.main',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\main.py',
   'PYMODULE'),
  ('numba.cuda.testing',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\testing.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvvm',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.enums',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\enums.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.rtapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\rtapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.error',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.libs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\libs.py',
   'PYMODULE'),
  ('numba.misc.findlib',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\findlib.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devices',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.driver',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devicearray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.kernels.transpose',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\kernels\\transpose.py',
   'PYMODULE'),
  ('numba.cuda.kernels',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\kernels\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.kernels.reduction',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\kernels\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.api_util',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\api_util.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.dummyarray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\dummyarray.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvrtc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\nvrtc.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.drvapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.cuda_paths',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cuda_paths.py',
   'PYMODULE'),
  ('numba.testing.loader',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\testing\\loader.py',
   'PYMODULE'),
  ('cProfile', 'D:\\SeaPython\\Lib\\cProfile.py', 'PYMODULE'),
  ('pstats', 'D:\\SeaPython\\Lib\\pstats.py', 'PYMODULE'),
  ('profile', 'D:\\SeaPython\\Lib\\profile.py', 'PYMODULE'),
  ('numba.typed',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\typed\\__init__.py',
   'PYMODULE'),
  ('numba.core.target_extension',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\target_extension.py',
   'PYMODULE'),
  ('numba.core.withcontexts',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\withcontexts.py',
   'PYMODULE'),
  ('numba.experimental',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\__init__.py',
   'PYMODULE'),
  ('numba.np.numpy_support',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\numpy_support.py',
   'PYMODULE'),
  ('numba.np.ufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.vectorizers',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\vectorizers.py',
   'PYMODULE'),
  ('numba.cuda.deviceufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\deviceufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.array_exprs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\array_exprs.py',
   'PYMODULE'),
  ('numba.np.ufunc.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\decorators.py',
   'PYMODULE'),
  ('numba.np.ufunc.gufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\gufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufunc_base',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\ufunc_base.py',
   'PYMODULE'),
  ('numba.np.ufunc.dufunc',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\dufunc.py',
   'PYMODULE'),
  ('numba.core.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\decorators.py',
   'PYMODULE'),
  ('numba.misc.special',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\special.py',
   'PYMODULE'),
  ('numba.core.errors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\errors.py',
   'PYMODULE'),
  ('colorama',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\SeaPython\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('numba.core.types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.function_type',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\function_type.py',
   'PYMODULE'),
  ('numba.core.types.scalars',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\scalars.py',
   'PYMODULE'),
  ('numba.core.types.npytypes',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\types\\npytypes.py',
   'PYMODULE'),
  ('numba.core.config',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\config.py',
   'PYMODULE'),
  ('numba.cuda',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.descriptor',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\descriptor.py',
   'PYMODULE'),
  ('numba.cuda.target',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\target.py',
   'PYMODULE'),
  ('numba.cuda.mathimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\mathimpl.py',
   'PYMODULE'),
  ('numba.cuda.dispatcher',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\dispatcher.py',
   'PYMODULE'),
  ('numba.cuda.types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\types.py',
   'PYMODULE'),
  ('numba.cuda.errors',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\errors.py',
   'PYMODULE'),
  ('numba.cuda.args',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\args.py',
   'PYMODULE'),
  ('numba.cuda.models',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\models.py',
   'PYMODULE'),
  ('numba.cuda.api',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\api.py',
   'PYMODULE'),
  ('numba.cuda.device_init',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\device_init.py',
   'PYMODULE'),
  ('numba.cuda.intrinsic_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\intrinsic_wrapper.py',
   'PYMODULE'),
  ('numba.cuda.decorators',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\decorators.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernel',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\kernel.py',
   'PYMODULE'),
  ('numba.cuda.simulator',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.compiler',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.runtime',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devices',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.nvvm',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.error',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.drvapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.driver',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.simulator.reduction',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.simulator.vector_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.simulator.api',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\api.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernelapi',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\kernelapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devicearray',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.intrinsics',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\intrinsics.py',
   'PYMODULE'),
  ('numba.cuda.extending',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\extending.py',
   'PYMODULE'),
  ('numba.cuda.stubs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\stubs.py',
   'PYMODULE'),
  ('numba.cuda.initialize',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\initialize.py',
   'PYMODULE'),
  ('numba.cuda.libdeviceimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdeviceimpl.py',
   'PYMODULE'),
  ('numba.cuda.printimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\printimpl.py',
   'PYMODULE'),
  ('numba.cuda.cudaimpl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudaimpl.py',
   'PYMODULE'),
  ('numba.cuda.vector_types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.libdevicedecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdevicedecl.py',
   'PYMODULE'),
  ('numba.cuda.libdevicefuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdevicefuncs.py',
   'PYMODULE'),
  ('numba.cuda.cudamath',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudamath.py',
   'PYMODULE'),
  ('numba.cuda.cudadecl',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadecl.py',
   'PYMODULE'),
  ('numba.cuda.ufuncs',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\ufuncs.py',
   'PYMODULE'),
  ('numba.cuda.libdevice',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\libdevice.py',
   'PYMODULE'),
  ('numba.cuda.codegen',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\codegen.py',
   'PYMODULE'),
  ('numba.cuda.cg',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cg.py',
   'PYMODULE'),
  ('numba.cuda.nvvmutils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\nvvmutils.py',
   'PYMODULE'),
  ('numba.cuda.simulator_init',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\simulator_init.py',
   'PYMODULE'),
  ('numba.runtests',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\runtests.py',
   'PYMODULE'),
  ('numba.types',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\types\\__init__.py',
   'PYMODULE'),
  ('numba.cext',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cext\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('numba.core',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\__init__.py',
   'PYMODULE'),
  ('numba.misc.init_utils',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\misc\\init_utils.py',
   'PYMODULE'),
  ('numba._version',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\_version.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pickletools', 'D:\\SeaPython\\Lib\\pickletools.py', 'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pytz', 'D:\\SeaPython\\Lib\\site-packages\\pytz\\__init__.py', 'PYMODULE'),
  ('pytz.tzfile',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy', 'D:\\SeaPython\\Lib\\site-packages\\pytz\\lazy.py', 'PYMODULE'),
  ('pytz.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\SeaPython\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\SeaPython\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__', 'D:\\SeaPython\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\SeaPython\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'D:\\SeaPython\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\SeaPython\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\SeaPython\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\SeaPython\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\SeaPython\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\SeaPython\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'D:\\SeaPython\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom', 'D:\\SeaPython\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('pandas.io.xml',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('fsspec',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\__init__.py',
   'PYMODULE'),
  ('fsspec.utils',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\utils.py',
   'PYMODULE'),
  ('fsspec.transaction',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\transaction.py',
   'PYMODULE'),
  ('fsspec.parquet',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\parquet.py',
   'PYMODULE'),
  ('fsspec.json',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\json.py',
   'PYMODULE'),
  ('fsspec.implementations.zip',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\zip.py',
   'PYMODULE'),
  ('fsspec.implementations.webhdfs',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\webhdfs.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('brotli', 'D:\\SeaPython\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('idna', 'D:\\SeaPython\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.package_data',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'D:\\SeaPython\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\SeaPython\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\SeaPython\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\SeaPython\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'D:\\SeaPython\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests._internal_utils',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\SeaPython\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks', 'D:\\SeaPython\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('requests.packages',
   'D:\\SeaPython\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('fsspec.implementations.tar',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\tar.py',
   'PYMODULE'),
  ('fsspec.implementations.smb',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\smb.py',
   'PYMODULE'),
  ('fsspec.implementations.sftp',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\sftp.py',
   'PYMODULE'),
  ('fsspec.implementations.reference',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\reference.py',
   'PYMODULE'),
  ('fsspec.implementations.memory',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\memory.py',
   'PYMODULE'),
  ('fsspec.implementations.local',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\local.py',
   'PYMODULE'),
  ('fsspec.implementations.libarchive',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\libarchive.py',
   'PYMODULE'),
  ('fsspec.implementations.jupyter',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\jupyter.py',
   'PYMODULE'),
  ('fsspec.implementations.http_sync',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\http_sync.py',
   'PYMODULE'),
  ('fsspec.implementations.http',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\http.py',
   'PYMODULE'),
  ('fsspec.implementations.github',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\github.py',
   'PYMODULE'),
  ('fsspec.implementations.git',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\git.py',
   'PYMODULE'),
  ('fsspec.implementations.gist',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\gist.py',
   'PYMODULE'),
  ('fsspec.implementations.ftp',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\ftp.py',
   'PYMODULE'),
  ('fsspec.implementations.dirfs',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\dirfs.py',
   'PYMODULE'),
  ('fsspec.implementations.dbfs',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\dbfs.py',
   'PYMODULE'),
  ('fsspec.implementations.data',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\data.py',
   'PYMODULE'),
  ('fsspec.implementations.dask',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\dask.py',
   'PYMODULE'),
  ('fsspec.implementations.cached',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\cached.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_metadata',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\cache_metadata.py',
   'PYMODULE'),
  ('fsspec.implementations.cache_mapper',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\cache_mapper.py',
   'PYMODULE'),
  ('fsspec.implementations.asyn_wrapper',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\asyn_wrapper.py',
   'PYMODULE'),
  ('fsspec.implementations.arrow',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\arrow.py',
   'PYMODULE'),
  ('fsspec.implementations',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\implementations\\__init__.py',
   'PYMODULE'),
  ('fsspec.gui',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\gui.py',
   'PYMODULE'),
  ('fsspec.generic',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\generic.py',
   'PYMODULE'),
  ('fsspec.fuse',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\fuse.py',
   'PYMODULE'),
  ('fsspec.dircache',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\dircache.py',
   'PYMODULE'),
  ('fsspec.conftest',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\conftest.py',
   'PYMODULE'),
  ('fsspec.config',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\config.py',
   'PYMODULE'),
  ('fsspec.asyn',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\asyn.py',
   'PYMODULE'),
  ('fsspec.archive',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\archive.py',
   'PYMODULE'),
  ('fsspec.spec',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\spec.py',
   'PYMODULE'),
  ('fsspec.registry',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\registry.py',
   'PYMODULE'),
  ('fsspec.mapping',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\mapping.py',
   'PYMODULE'),
  ('fsspec.exceptions',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\exceptions.py',
   'PYMODULE'),
  ('fsspec.core',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\core.py',
   'PYMODULE'),
  ('fsspec.compression',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\compression.py',
   'PYMODULE'),
  ('fsspec.callbacks',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\callbacks.py',
   'PYMODULE'),
  ('tqdm', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\__init__.py', 'PYMODULE'),
  ('tqdm.notebook',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.version',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm.std', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\std.py', 'PYMODULE'),
  ('tqdm.utils',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.gui', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\gui.py', 'PYMODULE'),
  ('tqdm.cli', 'D:\\SeaPython\\Lib\\site-packages\\tqdm\\cli.py', 'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'D:\\SeaPython\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('fsspec._version',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\_version.py',
   'PYMODULE'),
  ('fsspec.caching',
   'D:\\SeaPython\\Lib\\site-packages\\fsspec\\caching.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\SeaPython\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\SeaPython\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\SeaPython\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.dialog', 'D:\\SeaPython\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('datetime', 'D:\\SeaPython\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\SeaPython\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('threading', 'D:\\SeaPython\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\SeaPython\\Lib\\_threading_local.py', 'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\SeaPython\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\SeaPython\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\SeaPython\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\SeaPython\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter', 'D:\\SeaPython\\Lib\\tkinter\\__init__.py', 'PYMODULE')],
 [('python313.dll', 'D:\\SeaPython\\python313.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\SeaPython\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'D:\\SeaPython\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('llvmlite\\binding\\llvmlite.dll',
   'D:\\SeaPython\\Lib\\site-packages\\llvmlite\\binding\\llvmlite.dll',
   'BINARY'),
  ('_lzma.pyd', 'D:\\SeaPython\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\SeaPython\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\SeaPython\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\SeaPython\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\SeaPython\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\SeaPython\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\SeaPython\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'D:\\SeaPython\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\SeaPython\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\SeaPython\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\SeaPython\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\SeaPython\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\SeaPython\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\SeaPython\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\SeaPython\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_elementtree.pyd', 'D:\\SeaPython\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('lxml\\etree.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\etree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\_elementpath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\sax.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\objectify.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\html\\diff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\builder.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\json.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\index.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\core\\typeconv\\_typeconv.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\typeconv\\_typeconv.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\experimental\\jitclass\\_box.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\experimental\\jitclass\\_box.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\SeaPython\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('numba\\core\\runtime\\_nrt_python.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\core\\runtime\\_nrt_python.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\workqueue.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\workqueue.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\omppool.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\omppool.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\tbbpool.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\tbbpool.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\_internal.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\np\\ufunc\\_internal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\cuda\\cudadrv\\_extras.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\cuda\\cudadrv\\_extras.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_devicearray.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\_devicearray.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\mviewbuf.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\mviewbuf.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dispatcher.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\_dispatcher.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_helperlib.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\_helperlib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dynfunc.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\numba\\_dynfunc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\SeaPython\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_brotli.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\_brotli.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pyodbc.cp313-win_amd64.pyd',
   'D:\\SeaPython\\Lib\\site-packages\\pyodbc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'D:\\SeaPython\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\SeaPython\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\SeaPython\\VCRUNTIME140_1.dll', 'BINARY'),
  ('MSVCP140.dll', 'C:\\Windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\SeaPython\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\SeaPython\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\SeaPython\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'D:\\SeaPython\\python3.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'D:\\SeaPython\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('VCOMP140.DLL', 'C:\\Windows\\system32\\VCOMP140.DLL', 'BINARY'),
  ('sqlite3.dll', 'D:\\SeaPython\\DLLs\\sqlite3.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\SeaPython\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\SeaPython\\DLLs\\tcl86t.dll', 'BINARY'),
  ('zlib1.dll', 'D:\\SeaPython\\DLLs\\zlib1.dll', 'BINARY')],
 [],
 [],
 [('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\SeaPython\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\SeaPython\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\SeaPython\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\SeaPython\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\SeaPython\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\SeaPython\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\SeaPython\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tk_data\\scale.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\word.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\SeaPython\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tk_data\\button.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tk_data\\tkfbox.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\package.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\SeaPython\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tk_data\\msgs\\da.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\SeaPython\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\SeaPython\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tk_data\\focus.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tk_data\\text.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\SeaPython\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tk_data\\menu.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tk_data\\icons.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\history.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\GB', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tk_data\\msgs\\es.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'D:\\SeaPython\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tk_data\\entry.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tk_data\\console.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\SeaPython\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\init.tcl', 'D:\\SeaPython\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tclIndex', 'D:\\SeaPython\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tk_data\\palette.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\SeaPython\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tk_data\\tclIndex', 'D:\\SeaPython\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'D:\\SeaPython\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\SeaPython\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\SeaPython\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\SeaPython\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\SeaPython\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\SeaPython\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\SeaPython\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\SeaPython\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'D:\\SeaPython\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\SeaPython\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('numpy-2.2.4.dist-info\\INSTALLER',
   'D:\\SeaPython\\Lib\\site-packages\\numpy-2.2.4.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\SeaPython\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\LICENSE.txt',
   'D:\\SeaPython\\Lib\\site-packages\\numpy-2.2.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\SeaPython\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\WHEEL',
   'D:\\SeaPython\\Lib\\site-packages\\numpy-2.2.4.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\SeaPython\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\entry_points.txt',
   'D:\\SeaPython\\Lib\\site-packages\\numpy-2.2.4.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.4.dist-info\\RECORD',
   'D:\\SeaPython\\Lib\\site-packages\\numpy-2.2.4.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.4.dist-info\\DELVEWHEEL',
   'D:\\SeaPython\\Lib\\site-packages\\numpy-2.2.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\SeaPython\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\SeaPython\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\SeaPython\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\SeaPython\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.4.dist-info\\METADATA',
   'D:\\SeaPython\\Lib\\site-packages\\numpy-2.2.4.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\test\\build\\database_query_app\\base_library.zip',
   'DATA')])
