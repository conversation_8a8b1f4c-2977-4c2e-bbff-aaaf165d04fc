# SQL数据库查询工具

这是一个具有图形用户界面的Python应用程序，用于连接本地SQL Server数据库，查找UFDATA_开头的数据库，并查询GL_accvouch表的数据。

## 功能特性

- 🔗 连接本地SQL Server数据库
- 🔍 自动扫描所有UFDATA_开头的数据库
- 📊 查询GL_accvouch表的所有数据
- 🖥️ 现代化的图形用户界面设计
- 📋 美观的表格展示，支持交替行颜色
- ⚡ 多线程操作，界面响应流畅
- 📊 数据导出功能（Excel格式，支持CSV备选）
- 🔢 自动添加序列号列
- 🔍 双击查看记录详情
- 🎨 优化的界面布局和图标
- ⚙️ 可配置的连接参数
- 📊 实时显示记录统计

## 系统要求

- Python 3.6 或更高版本
- Windows 操作系统
- 本地安装SQL Server
- ODBC驱动程序

## 安装步骤

### 1. 安装Python依赖包

```bash
pip install -r requirements.txt
```

### 2. 确保ODBC驱动已安装

如果您的系统没有安装ODBC驱动，请下载并安装：
- [Microsoft ODBC Driver 17 for SQL Server](https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server)

## 使用方法

### 1. 运行程序

```bash
python database_query_app.py
```

### 2. 连接数据库

1. 在"服务器"输入框中输入SQL Server服务器地址（默认为localhost）
2. 点击"连接数据库"按钮
3. 程序会自动扫描所有UFDATA_开头的数据库

### 3. 查询数据

1. 从下拉列表中选择要查询的数据库
2. 点击"查询 GL_accvouch"按钮
3. 查询结果将在下方的表格中显示

## 界面说明

### 数据库连接配置区域
- **服务器地址输入框**: 输入SQL Server服务器地址（支持实例名）
- **🔗 连接数据库按钮**: 连接到数据库并自动扫描UFDATA数据库

### 查询配置区域
- **数据库下拉列表**: 选择要查询的UFDATA数据库
- **📊 查询按钮**: 执行SELECT * FROM GL_accvouch查询
- **📊 导出Excel按钮**: 将查询结果导出为Excel文件

### 状态信息区域
- **状态标签**: 显示当前操作状态（带图标提示）
- **记录统计**: 显示查询结果的记录数量

### 结果显示区域
- **结果表格**: 美观的表格显示，支持：
  - 🔢 自动添加序列号列
  - 🎨 交替行颜色显示
  - 📜 水平和垂直滚动
  - 🔍 双击查看记录详情
  - 📏 自动调整列宽
  - 📐 居中对齐显示

## 故障排除

### 连接失败
1. 确保SQL Server服务正在运行
2. 检查服务器地址是否正确
3. 确认您有访问数据库的权限
4. 检查防火墙设置

### 找不到UFDATA数据库
1. 确认数据库名称以"UFDATA_"开头
2. 检查您是否有查看数据库列表的权限

### 查询失败
1. 确认GL_accvouch表存在于选定的数据库中
2. 检查您是否有查询该表的权限

## 技术说明

- **GUI框架**: tkinter (Python内置)
- **数据库连接**: pyodbc
- **多线程**: 防止界面卡顿
- **错误处理**: 完善的异常处理机制

## 注意事项

- 程序使用Windows身份验证连接数据库
- 如果需要使用SQL Server身份验证，请修改连接字符串
- 大量数据查询可能需要一些时间，请耐心等待
- 程序会自动处理数据库连接的关闭
