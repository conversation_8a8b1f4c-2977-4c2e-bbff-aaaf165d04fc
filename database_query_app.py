#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL数据库查询工具
功能：连接本地SQL数据库，查找UFDATA_开头的数据库，查询GL_accvouch表
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import pyodbc
import threading
from datetime import datetime


class DatabaseQueryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SQL数据库查询工具")
        self.root.geometry("1000x700")
        
        # 数据库连接参数
        self.server = "localhost"  # 本地服务器
        self.driver = "{ODBC Driver 17 for SQL Server}"  # 或者使用 "{SQL Server}"
        self.connection = None
        self.databases = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="SQL数据库查询工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 服务器连接配置
        ttk.Label(main_frame, text="服务器:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.server_entry = ttk.Entry(main_frame, width=30)
        self.server_entry.insert(0, self.server)
        self.server_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # 连接按钮
        self.connect_btn = ttk.Button(main_frame, text="连接数据库", 
                                     command=self.connect_database)
        self.connect_btn.grid(row=1, column=2, pady=5, padx=(10, 0))
        
        # 数据库选择
        ttk.Label(main_frame, text="选择数据库:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.database_var = tk.StringVar()
        self.database_combo = ttk.Combobox(main_frame, textvariable=self.database_var, 
                                          state="readonly", width=40)
        self.database_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # 查询按钮
        self.query_btn = ttk.Button(main_frame, text="查询 GL_accvouch", 
                                   command=self.query_data, state="disabled")
        self.query_btn.grid(row=2, column=2, pady=5, padx=(10, 0))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="请先连接数据库", 
                                     foreground="blue")
        self.status_label.grid(row=3, column=0, columnspan=3, pady=10)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="查询结果", padding="5")
        result_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview用于显示结果
        self.tree = ttk.Treeview(result_frame)
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(result_frame, orient="horizontal", command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
    def connect_database(self):
        """连接数据库并获取UFDATA_开头的数据库列表"""
        self.server = self.server_entry.get().strip()
        if not self.server:
            messagebox.showerror("错误", "请输入服务器地址")
            return
            
        self.status_label.config(text="正在连接数据库...", foreground="orange")
        self.connect_btn.config(state="disabled")
        
        # 在新线程中执行连接操作
        threading.Thread(target=self._connect_database_thread, daemon=True).start()
        
    def _connect_database_thread(self):
        """在后台线程中连接数据库"""
        try:
            # 尝试不同的连接字符串
            connection_strings = [
                f"DRIVER={self.driver};SERVER={self.server};Trusted_Connection=yes;",
                f"DRIVER={{SQL Server}};SERVER={self.server};Trusted_Connection=yes;",
                f"DRIVER={self.driver};SERVER={self.server};UID=sa;PWD=;",
            ]
            
            connected = False
            for conn_str in connection_strings:
                try:
                    self.connection = pyodbc.connect(conn_str, timeout=10)
                    connected = True
                    break
                except pyodbc.Error:
                    continue
                    
            if not connected:
                raise Exception("无法连接到数据库，请检查服务器地址和权限")
                
            # 获取UFDATA_开头的数据库
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sys.databases WHERE name LIKE 'UFDATA_%'")
            self.databases = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            # 更新UI
            self.root.after(0, self._update_ui_after_connect)
            
        except Exception as e:
            error_msg = f"连接失败: {str(e)}"
            self.root.after(0, lambda: self._show_connection_error(error_msg))
            
    def _update_ui_after_connect(self):
        """连接成功后更新UI"""
        if self.databases:
            self.database_combo['values'] = self.databases
            self.database_combo.set(self.databases[0])
            self.query_btn.config(state="normal")
            self.status_label.config(text=f"连接成功！找到 {len(self.databases)} 个UFDATA数据库", 
                                   foreground="green")
        else:
            self.status_label.config(text="连接成功，但未找到UFDATA_开头的数据库", 
                                   foreground="orange")
            
        self.connect_btn.config(state="normal")
        
    def _show_connection_error(self, error_msg):
        """显示连接错误"""
        self.status_label.config(text="连接失败", foreground="red")
        self.connect_btn.config(state="normal")
        messagebox.showerror("连接错误", error_msg)
        
    def query_data(self):
        """查询选定数据库的GL_accvouch表"""
        selected_db = self.database_var.get()
        if not selected_db:
            messagebox.showwarning("警告", "请选择一个数据库")
            return
            
        self.status_label.config(text="正在查询数据...", foreground="orange")
        self.query_btn.config(state="disabled")
        
        # 在新线程中执行查询
        threading.Thread(target=self._query_data_thread, args=(selected_db,), daemon=True).start()
        
    def _query_data_thread(self, database_name):
        """在后台线程中查询数据"""
        try:
            # 切换到选定的数据库
            cursor = self.connection.cursor()
            cursor.execute(f"USE [{database_name}]")
            
            # 检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'GL_accvouch'
            """)
            
            if cursor.fetchone()[0] == 0:
                raise Exception(f"表 GL_accvouch 在数据库 {database_name} 中不存在")
            
            # 执行查询
            cursor.execute("SELECT * FROM GL_accvouch")
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()
            cursor.close()
            
            # 更新UI显示结果
            self.root.after(0, lambda: self._display_results(columns, rows, database_name))
            
        except Exception as e:
            error_msg = f"查询失败: {str(e)}"
            self.root.after(0, lambda: self._show_query_error(error_msg))
            
    def _display_results(self, columns, rows, database_name):
        """显示查询结果"""
        # 清空之前的结果
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 设置列
        self.tree["columns"] = columns
        self.tree["show"] = "headings"
        
        # 配置列标题和宽度
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, minwidth=50)
            
        # 插入数据
        for row in rows:
            # 处理可能的None值
            processed_row = [str(item) if item is not None else "" for item in row]
            self.tree.insert("", "end", values=processed_row)
            
        self.status_label.config(
            text=f"查询完成！数据库: {database_name}, 共 {len(rows)} 条记录", 
            foreground="green"
        )
        self.query_btn.config(state="normal")
        
    def _show_query_error(self, error_msg):
        """显示查询错误"""
        self.status_label.config(text="查询失败", foreground="red")
        self.query_btn.config(state="normal")
        messagebox.showerror("查询错误", error_msg)
        
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if hasattr(self, 'connection') and self.connection:
            self.connection.close()


def main():
    """主函数"""
    root = tk.Tk()
    app = DatabaseQueryApp(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if hasattr(app, 'connection') and app.connection:
            app.connection.close()
        root.destroy()
        
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
