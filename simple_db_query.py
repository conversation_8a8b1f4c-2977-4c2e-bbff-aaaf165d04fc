#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版SQL数据库查询工具
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyodbc


class SimpleDBQuery:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简化版数据库查询工具")
        self.root.geometry("800x600")
        
        self.connection = None
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        frame = ttk.Frame(self.root, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 服务器输入
        ttk.Label(frame, text="服务器:").pack(anchor=tk.W)
        self.server_entry = ttk.Entry(frame, width=50)
        self.server_entry.insert(0, "localhost")
        self.server_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 连接按钮
        ttk.Button(frame, text="连接并查找UFDATA数据库", 
                  command=self.connect_and_find_databases).pack(pady=5)
        
        # 数据库选择
        ttk.Label(frame, text="选择数据库:").pack(anchor=tk.W, pady=(10, 0))
        self.db_var = tk.StringVar()
        self.db_combo = ttk.Combobox(frame, textvariable=self.db_var, state="readonly")
        self.db_combo.pack(fill=tk.X, pady=(0, 10))
        
        # 查询按钮
        ttk.Button(frame, text="查询 GL_accvouch 表", 
                  command=self.query_table).pack(pady=5)
        
        # 状态标签
        self.status_label = ttk.Label(frame, text="请先连接数据库")
        self.status_label.pack(pady=10)
        
        # 结果显示
        self.result_text = tk.Text(frame, height=20, width=80)
        self.result_text.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.result_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.result_text.yview)
        
    def connect_and_find_databases(self):
        server = self.server_entry.get().strip()
        if not server:
            messagebox.showerror("错误", "请输入服务器地址")
            return
            
        try:
            # 尝试连接数据库
            conn_str = f"DRIVER={{SQL Server}};SERVER={server};Trusted_Connection=yes;"
            self.connection = pyodbc.connect(conn_str)
            
            # 查找UFDATA数据库
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sys.databases WHERE name LIKE 'UFDATA_%'")
            databases = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            if databases:
                self.db_combo['values'] = databases
                self.db_combo.set(databases[0])
                self.status_label.config(text=f"找到 {len(databases)} 个UFDATA数据库")
            else:
                self.status_label.config(text="未找到UFDATA数据库")
                
        except Exception as e:
            messagebox.showerror("连接错误", f"连接失败: {str(e)}")
            self.status_label.config(text="连接失败")
            
    def query_table(self):
        if not self.connection:
            messagebox.showwarning("警告", "请先连接数据库")
            return
            
        selected_db = self.db_var.get()
        if not selected_db:
            messagebox.showwarning("警告", "请选择数据库")
            return
            
        try:
            cursor = self.connection.cursor()
            cursor.execute(f"USE [{selected_db}]")
            
            # 检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'GL_accvouch'
            """)
            
            if cursor.fetchone()[0] == 0:
                messagebox.showerror("错误", f"表 GL_accvouch 在数据库 {selected_db} 中不存在")
                return
                
            # 查询数据
            cursor.execute("SELECT * FROM GL_accvouch")
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()
            
            # 显示结果
            self.result_text.delete(1.0, tk.END)
            
            # 显示列标题
            header = "\t".join(columns) + "\n"
            self.result_text.insert(tk.END, header)
            self.result_text.insert(tk.END, "-" * 100 + "\n")
            
            # 显示数据
            for row in rows:
                row_text = "\t".join([str(item) if item is not None else "" for item in row]) + "\n"
                self.result_text.insert(tk.END, row_text)
                
            self.status_label.config(text=f"查询完成，共 {len(rows)} 条记录")
            cursor.close()
            
        except Exception as e:
            messagebox.showerror("查询错误", f"查询失败: {str(e)}")
            
    def run(self):
        self.root.mainloop()
        
    def __del__(self):
        if self.connection:
            self.connection.close()


if __name__ == "__main__":
    app = SimpleDBQuery()
    app.run()
