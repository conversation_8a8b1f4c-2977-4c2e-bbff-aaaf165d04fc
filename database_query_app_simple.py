#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL数据库查询工具 - 简化版
功能：连接本地SQL数据库，查找UFDATA_开头的数据库，查询GL_accvouch表
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pyodbc
import threading
import csv


class DatabaseQueryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SQL数据库查询工具")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 数据库连接参数
        self.server = "localhost"
        self.connection = None
        self.databases = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="SQL数据库查询工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 连接配置框架
        config_frame = ttk.LabelFrame(main_frame, text="数据库连接配置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 服务器连接配置
        ttk.Label(config_frame, text="服务器地址:").grid(
            row=0, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        self.server_entry = ttk.Entry(config_frame, width=35)
        self.server_entry.insert(0, self.server)
        self.server_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(config_frame, text="连接数据库", 
                                     command=self.connect_database)
        self.connect_btn.grid(row=0, column=2, pady=5)
        
        # 查询配置框架
        query_frame = ttk.LabelFrame(main_frame, text="查询配置", padding="10")
        query_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        query_frame.columnconfigure(1, weight=1)
        
        # 数据库选择
        ttk.Label(query_frame, text="选择数据库:").grid(
            row=0, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        self.database_var = tk.StringVar()
        self.database_combo = ttk.Combobox(query_frame, textvariable=self.database_var, 
                                          state="readonly", width=45)
        self.database_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(0, 10))
        
        # 按钮框架
        btn_frame = ttk.Frame(query_frame)
        btn_frame.grid(row=0, column=2, pady=5)
        
        # 查询按钮
        self.query_btn = ttk.Button(btn_frame, text="查询 GL_accvouch", 
                                   command=self.query_data, state="disabled")
        self.query_btn.grid(row=0, column=0, padx=(0, 5))
        
        # 导出按钮
        self.export_btn = ttk.Button(btn_frame, text="导出数据", 
                                    command=self.export_data, state="disabled")
        self.export_btn.grid(row=0, column=1)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="请先连接数据库", 
                                     foreground="blue")
        self.status_label.grid(row=3, column=0, columnspan=3, pady=10)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="查询结果", padding="5")
        result_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview用于显示结果
        self.tree = ttk.Treeview(result_frame, show='headings')
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(result_frame, orient="horizontal", command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', self.on_item_double_click)
        
    def connect_database(self):
        """连接数据库并获取UFDATA_开头的数据库列表"""
        self.server = self.server_entry.get().strip()
        if not self.server:
            messagebox.showerror("错误", "请输入服务器地址")
            return
            
        self.status_label.config(text="正在连接数据库...", foreground="orange")
        self.connect_btn.config(state="disabled")
        
        # 在新线程中执行连接操作
        threading.Thread(target=self._connect_database_thread, daemon=True).start()
        
    def _connect_database_thread(self):
        """在后台线程中连接数据库"""
        try:
            # 尝试不同的连接字符串
            connection_strings = [
                f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server};Trusted_Connection=yes;",
                f"DRIVER={{SQL Server}};SERVER={self.server};Trusted_Connection=yes;",
            ]
            
            connected = False
            for conn_str in connection_strings:
                try:
                    self.connection = pyodbc.connect(conn_str, timeout=10)
                    connected = True
                    break
                except pyodbc.Error:
                    continue
                    
            if not connected:
                raise Exception("无法连接到数据库，请检查服务器地址和权限")
                
            # 获取UFDATA_开头的数据库
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sys.databases WHERE name LIKE 'UFDATA_%'")
            self.databases = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            # 更新UI
            self.root.after(0, self._update_ui_after_connect)
            
        except Exception as e:
            error_msg = f"连接失败: {str(e)}"
            self.root.after(0, lambda: self._show_connection_error(error_msg))
            
    def _update_ui_after_connect(self):
        """连接成功后更新UI"""
        if self.databases:
            self.database_combo['values'] = self.databases
            self.database_combo.set(self.databases[0])
            self.query_btn.config(state="normal")
            self.status_label.config(text=f"连接成功！找到 {len(self.databases)} 个UFDATA数据库", 
                                   foreground="green")
        else:
            self.status_label.config(text="连接成功，但未找到UFDATA_开头的数据库", 
                                   foreground="orange")
            
        self.connect_btn.config(state="normal")
        
    def _show_connection_error(self, error_msg):
        """显示连接错误"""
        self.status_label.config(text="连接失败", foreground="red")
        self.connect_btn.config(state="normal")
        messagebox.showerror("连接错误", error_msg)
        
    def query_data(self):
        """查询选定数据库的GL_accvouch表"""
        selected_db = self.database_var.get()
        if not selected_db:
            messagebox.showwarning("警告", "请选择一个数据库")
            return
            
        self.status_label.config(text="正在查询数据...", foreground="orange")
        self.query_btn.config(state="disabled")
        
        # 在新线程中执行查询
        threading.Thread(target=self._query_data_thread, args=(selected_db,), daemon=True).start()
        
    def _query_data_thread(self, database_name):
        """在后台线程中查询数据"""
        try:
            # 切换到选定的数据库
            cursor = self.connection.cursor()
            cursor.execute(f"USE [{database_name}]")
            
            # 检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'GL_accvouch'
            """)
            
            if cursor.fetchone()[0] == 0:
                raise Exception(f"表 GL_accvouch 在数据库 {database_name} 中不存在")
            
            # 执行查询
            cursor.execute("SELECT * FROM GL_accvouch")
            columns = [column[0] for column in cursor.description]
            rows = cursor.fetchall()
            cursor.close()
            
            # 更新UI显示结果
            self.root.after(0, lambda: self._display_results(columns, rows, database_name))
            
        except Exception as e:
            error_msg = f"查询失败: {str(e)}"
            self.root.after(0, lambda: self._show_query_error(error_msg))
            
    def _display_results(self, columns, rows, database_name):
        """显示查询结果"""
        # 清空之前的结果
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 设置列
        self.tree["columns"] = columns
        self.tree["show"] = "headings"
        
        # 配置列标题和宽度
        for col in columns:
            self.tree.heading(col, text=col, anchor='center')
            # 根据列名调整宽度
            if col.lower() in ['id', 'ino_id', 'inid', 'iperiod']:
                width = 80
            elif col.lower() in ['dbill_date', 'ccode']:
                width = 120
            else:
                width = 120
            self.tree.column(col, width=width, minwidth=60, anchor='center')
            
        # 插入数据并添加交替行颜色
        for i, row in enumerate(rows):
            # 处理可能的None值
            processed_row = [str(item) if item is not None else "" for item in row]
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.tree.insert("", "end", values=processed_row, tags=(tag,))
            
        # 配置行颜色
        self.tree.tag_configure('evenrow', background='#f8f9fa')
        self.tree.tag_configure('oddrow', background='white')
        
        self.status_label.config(
            text=f"查询完成！数据库: {database_name}, 共 {len(rows)} 条记录", 
            foreground="green"
        )
        self.query_btn.config(state="normal")
        self.export_btn.config(state="normal")
        
    def _show_query_error(self, error_msg):
        """显示查询错误"""
        self.status_label.config(text="查询失败", foreground="red")
        self.query_btn.config(state="normal")
        messagebox.showerror("查询错误", error_msg)
        
    def on_item_double_click(self, event):
        """双击表格项时显示详细信息"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if item:
            values = self.tree.item(item, 'values')
            columns = self.tree["columns"]
            
            # 创建详细信息窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title("记录详情")
            detail_window.geometry("500x400")
            
            # 创建文本框
            text_frame = ttk.Frame(detail_window, padding="10")
            text_frame.pack(fill=tk.BOTH, expand=True)
            
            text_widget = tk.Text(text_frame, font=('Arial', 10), wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True)
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(text_frame, command=text_widget.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            text_widget.config(yscrollcommand=scrollbar.set)
            
            # 显示详细信息
            for col, val in zip(columns, values):
                text_widget.insert(tk.END, f"{col}: {val}\n")
                
    def export_data(self):
        """导出数据到CSV文件"""
        if not hasattr(self, 'tree') or not self.tree.get_children():
            messagebox.showwarning("警告", "没有数据可以导出")
            return
            
        try:
            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="保存数据"
            )
            
            if filename:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # 写入列标题
                    columns = self.tree["columns"]
                    writer.writerow(columns)
                    
                    # 写入数据
                    for item in self.tree.get_children():
                        values = self.tree.item(item, 'values')
                        writer.writerow(values)
                        
                messagebox.showinfo("成功", f"数据已导出到: {filename}")
                
        except Exception as e:
            messagebox.showerror("导出错误", f"导出失败: {str(e)}")
        
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if hasattr(self, 'connection') and self.connection:
            self.connection.close()


def main():
    """主函数"""
    root = tk.Tk()
    app = DatabaseQueryApp(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if hasattr(app, 'connection') and app.connection:
            app.connection.close()
        root.destroy()
        
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
