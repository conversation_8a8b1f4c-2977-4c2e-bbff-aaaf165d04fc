@echo off
chcp 65001 >nul
title SQL数据库查询工具
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    SQL数据库查询工具                          ║
echo ║                                                              ║
echo ║  功能: 连接本地SQL数据库，查询UFDATA数据库中的GL_accvouch表    ║
echo ║  版本: 2.0 优化版                                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 请选择要运行的版本:
echo 1. 完整版 (推荐) - 现代化界面，丰富功能
echo 2. 简化版 - 兼容性更好，界面简洁
echo.
set /p choice=请输入选择 (1 或 2):

if "%choice%"=="1" (
    echo.
    echo 正在启动完整版程序...
    python database_query_app.py
) else if "%choice%"=="2" (
    echo.
    echo 正在启动简化版程序...
    python database_query_app_simple.py
) else (
    echo.
    echo 无效选择，启动默认版本...
    python database_query_app.py
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 程序运行出错，错误代码: %ERRORLEVEL%
    echo.
    echo 可能的解决方案:
    echo 1. 确保已安装Python
    echo 2. 确保已安装pyodbc库: pip install pyodbc
    echo 3. 检查SQL Server是否正在运行
    echo.
) else (
    echo.
    echo ✅ 程序正常退出
    echo.
)

pause
