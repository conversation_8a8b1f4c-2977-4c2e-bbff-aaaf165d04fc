@echo off
chcp 65001 >nul
title SQL数据库查询工具
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    SQL数据库查询工具                          ║
echo ║                                                              ║
echo ║  功能: 连接本地SQL数据库，查询UFDATA数据库中的GL_accvouch表    ║
echo ║  版本: 2.0 优化版                                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 正在启动程序...
echo.

python database_query_app.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 程序运行出错，错误代码: %ERRORLEVEL%
    echo.
    echo 可能的解决方案:
    echo 1. 确保已安装Python
    echo 2. 确保已安装pyodbc库: pip install pyodbc
    echo 3. 检查SQL Server是否正在运行
    echo.
) else (
    echo.
    echo ✅ 程序正常退出
    echo.
)

pause
